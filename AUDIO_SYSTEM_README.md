# 🎵 音频系统使用说明

## 📁 音频资源

### 背景音乐 (BGM)
| 音频文件 | 功能描述 | 使用场景 | 播放特性 |
|---------|---------|---------|---------|
| `menu.mp3` | 菜单背景音乐 | 游戏启动页面、主菜单界面、游戏结束后返回菜单 | 循环播放，音量0.3 |
| `gameplay.mp3` | 游戏背景音乐 | 游戏进行中的背景音乐 | 循环播放，音量0.3 |
| `win.mp3` | 胜利背景音乐 | 成功合成富老炮后的胜利状态 | 循环播放，音量0.4 |
| `lose.mp3` | 失败背景音乐 | 游戏失败后的背景音乐 | 循环播放，音量0.4 |

### 游戏音效 (SFX)
| 音效文件 | 功能描述 | 触发场景 | 播放特性 |
|---------|---------|---------|---------|
| `click.mp3` | 点击音效 | 按钮点击、界面交互、开始游戏 | 单次播放，音量0.6 |
| `drop.mp3` | 投放音效 | 角色投放到游戏区域时 | 单次播放，音量0.6 |
| `synthesis.mp3` | 合成音效 | 两个相同角色成功合成时 | 单次播放，音量0.6 |
| `transform.mp3` | 转换音效 | 点击丐帮主转换为其他角色时 | 单次播放，音量0.6 |

## 🎮 使用方法

### 1. 自动BGM切换
游戏会根据当前场景自动切换背景音乐：

```javascript
// 在main.js中已集成
this.audioManager.switchBGMByGameState('menu')     // 菜单场景
this.audioManager.switchBGMByGameState('gameplay') // 游戏场景
this.audioManager.switchBGMByGameState('win')      // 胜利场景
this.audioManager.switchBGMByGameState('lose')     // 失败场景
```

### 2. 音效播放
在各个场景中调用相应的音效方法：

```javascript
// 点击音效
this.main.audioManager.playClickSound()

// 投放音效
this.main.audioManager.playDropSound()

// 合成音效
this.main.audioManager.playSynthesisSound()

// 转换音效
this.main.audioManager.playTransformSound()
```

### 3. 静音控制
在主菜单右上角有静音按钮，可以控制全局音频开关：

```javascript
// 切换静音状态
this.main.audioManager.toggleMute()

// 设置静音
this.main.audioManager.mute()

// 取消静音
this.main.audioManager.unmute()
```

### 4. 音量控制
可以分别控制BGM和音效的音量：

```javascript
// 设置背景音乐音量 (0.0 - 1.0)
this.main.audioManager.setBGMVolume(0.5)

// 设置音效音量 (0.0 - 1.0)
this.main.audioManager.setSFXVolume(0.8)
```

## 🔧 技术特性

### 音频状态管理
- ✅ **全局静音控制**：影响所有音频播放
- ✅ **状态切换**：根据游戏状态自动切换对应背景音乐
- ✅ **音效叠加**：音效可与背景音乐同时播放
- ✅ **资源清理**：音频播放完成后自动销毁上下文

### 性能优化
- ✅ **预加载**：游戏启动时预加载所有音效
- ✅ **对象池**：音效播放时创建新实例，避免冲突
- ✅ **自动清理**：播放完成后自动销毁音频对象
- ✅ **错误处理**：音频加载失败时的优雅降级

### 微信小游戏适配
- ✅ **InnerAudioContext**：使用微信小游戏音频API
- ✅ **循环播放**：BGM支持循环播放
- ✅ **音量控制**：支持独立音量调节
- ✅ **事件监听**：监听播放完成事件

## 🎯 集成位置

### 已集成的音效
1. **MenuScene.js** - 按钮点击音效、静音控制
2. **GameScene.js** - 投放音效、合成音效、转换音效
3. **GameOverScene.js** - 按钮点击音效
4. **main.js** - 自动BGM切换

### 静音按钮
- 位置：主菜单右上角
- 图标：🔊 (有声音) / 🔇 (静音)
- 功能：切换全局静音状态

## 🚀 扩展功能

### 添加新音效
1. 将音频文件放入 `game_media/audio/` 目录
2. 在 `AudioManager.js` 的 `audioConfig.sfx` 中添加配置
3. 添加对应的播放方法
4. 在需要的地方调用播放方法

### 添加新BGM
1. 将音频文件放入 `game_media/audio/` 目录
2. 在 `AudioManager.js` 的 `audioConfig.bgm` 中添加配置
3. 在 `switchBGMByGameState` 方法中添加新的状态处理
4. 在场景切换时调用对应的BGM切换

## 📝 注意事项

1. **文件路径**：确保音频文件路径正确
2. **文件格式**：支持MP3格式
3. **文件大小**：注意音频文件大小，避免影响加载速度
4. **兼容性**：在微信小游戏环境中测试音频播放
5. **用户体验**：提供静音选项，尊重用户偏好

## 🎉 完成状态

✅ **音频系统已完全集成**
✅ **所有音效已配置**
✅ **BGM自动切换已实现**
✅ **静音控制已添加**
✅ **音量控制已实现**
✅ **错误处理已完善**
✅ **性能优化已完成**

音频系统现已完全可用，为游戏提供了丰富的听觉体验！🎵✨ 