/**
 * 简化物理引擎备用方案
 * 当微信物理引擎不可用时使用
 */

export class SimplePhysicsFallback {
  constructor() {
    this.bodies = new Map()
    this.isLoaded = true
    this.gravity = { x: 0, y: 9.8 }
    this.timeStep = 1/60
  }

  /**
   * 创建角色刚体
   */
  createFruitBody(fruit, x, y) {
    const body = {
      position: { x: x / 30, y: y / 30 },
      velocity: { x: 0, y: 0 },
      force: { x: 0, y: 0 },
      mass: fruit.radius / 10,
      isStatic: false,
      fruit: fruit
    }
    this.bodies.set(fruit, body)
    console.log(`角色 ${fruit.name} 简化物理刚体创建成功`)
    return body
  }

  /**
   * 移除角色刚体
   */
  removeFruitBody(fruit) {
    this.bodies.delete(fruit)
    console.log(`角色 ${fruit.name} 简化物理刚体已移除`)
  }

  /**
   * 物理步进
   */
  step(timeStep = this.timeStep) {
    this.bodies.forEach((body, fruit) => {
      if (body.isStatic) return
      
      // 应用重力
      body.force.y += this.gravity.y * body.mass
      
      // 计算加速度
      const acceleration = {
        x: body.force.x / body.mass,
        y: body.force.y / body.mass
      }
      
      // 更新速度
      body.velocity.x += acceleration.x * timeStep
      body.velocity.y += acceleration.y * timeStep
      
      // 应用阻尼
      body.velocity.x *= 0.98
      body.velocity.y *= 0.98
      
      // 更新位置
      body.position.x += body.velocity.x * timeStep
      body.position.y += body.velocity.y * timeStep
      
      // 同步到水果对象
      fruit.x = body.position.x * 30
      fruit.y = body.position.y * 30
      fruit.vx = body.velocity.x
      fruit.vy = body.velocity.y
      
      // 重置力
      body.force.x = 0
      body.force.y = 0
    })
  }

  /**
   * 设置角色位置
   */
  setFruitPosition(fruit, x, y) {
    const body = this.bodies.get(fruit)
    if (body) {
      body.position.x = x / 30
      body.position.y = y / 30
    }
  }

  /**
   * 对角色施加力
   */
  applyForceToFruit(fruit, forceX, forceY) {
    const body = this.bodies.get(fruit)
    if (body) {
      body.force.x += forceX
      body.force.y += forceY
    }
  }

  /**
   * 设置合成回调
   */
  setMergeCallback(callback) {
    this.mergeCallback = callback
  }

  /**
   * 设置重力
   */
  setGravity(x, y) {
    this.gravity = { x: x, y: y }
  }

  /**
   * 获取重力
   */
  getGravity() {
    return this.gravity
  }

  /**
   * 销毁物理引擎
   */
  destroy() {
    this.bodies.clear()
    console.log('简化物理引擎已销毁')
  }

  /**
   * 获取状态
   */
  getStatus() {
    return {
      isLoaded: this.isLoaded,
      bodyCount: this.bodies.size,
      gravity: this.gravity,
      timeStep: this.timeStep
    }
  }
}

/**
 * 简化物理引擎初始化函数
 */
export function initSimplePhysics() {
  console.log('初始化简化物理引擎...')
  const physicsEngine = new SimplePhysicsFallback()
  
  const result = {
    useSimplePhysics: true,
    isLoaded: true,
    world: null,
    bodies: physicsEngine.bodies,
    createFruitBody: physicsEngine.createFruitBody.bind(physicsEngine),
    removeFruitBody: physicsEngine.removeFruitBody.bind(physicsEngine),
    step: physicsEngine.step.bind(physicsEngine),
    setFruitPosition: physicsEngine.setFruitPosition.bind(physicsEngine),
    applyForceToFruit: physicsEngine.applyForceToFruit.bind(physicsEngine),
    setMergeCallback: physicsEngine.setMergeCallback.bind(physicsEngine),
    setGravity: physicsEngine.setGravity.bind(physicsEngine),
    getGravity: physicsEngine.getGravity.bind(physicsEngine),
    destroy: physicsEngine.destroy.bind(physicsEngine),
    getStatus: physicsEngine.getStatus.bind(physicsEngine),
    engine: physicsEngine
  }
  
  console.log('简化物理引擎初始化成功')
  return result
} 