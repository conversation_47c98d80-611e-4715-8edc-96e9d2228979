// 简化物理引擎 - 模拟 Box2D 的基本功能
// 当 Box2D WASM 不可用时使用此引擎

export class SimplePhysicsEngine {
  constructor() {
    this.bodies = new Map()
    this.gravity = { x: 0, y: 9.8 }
    this.timeStep = 1/60
    this.isLoaded = true
    this.useSimplePhysics = true
  }

  async load() {
    // 简化物理引擎无需加载
    console.log('使用简化物理引擎')
    return Promise.resolve()
  }

  // 创建水果刚体
  createFruitBody(fruit) {
    const body = {
      fruit: fruit,
      position: { x: fruit.x / 30, y: fruit.y / 30 },
      velocity: { x: 0, y: 0 },
      force: { x: 0, y: 0 },
      mass: 1.0,
      radius: fruit.radius / 30,
      isStatic: false,
      userData: fruit
    }
    
    this.bodies.set(fruit, body)
    fruit.body = body
    return body
  }

  // 移除水果刚体
  removeFruitBody(fruit) {
    this.bodies.delete(fruit)
    if (fruit.body) {
      fruit.body = null
    }
  }

  // 更新物理世界
  step(timeStep = 1/60, velocityIterations = 6, positionIterations = 2) {
    this.bodies.forEach((body, fruit) => {
      if (body.isStatic) return

      // 应用重力
      body.force.y += this.gravity.y * body.mass

      // 计算加速度
      const acceleration = {
        x: body.force.x / body.mass,
        y: body.force.y / body.mass
      }

      // 更新速度
      body.velocity.x += acceleration.x * timeStep
      body.velocity.y += acceleration.y * timeStep

      // 应用阻尼
      body.velocity.x *= 0.98
      body.velocity.y *= 0.98

      // 更新位置
      body.position.x += body.velocity.x * timeStep
      body.position.y += body.velocity.y * timeStep

      // 同步到水果对象
      fruit.x = body.position.x * 30
      fruit.y = body.position.y * 30

      // 检查是否静止
      const speed = Math.sqrt(body.velocity.x * body.velocity.x + body.velocity.y * body.velocity.y)
      if (speed < 0.1) {
        fruit.isStatic = true
      }

      // 重置力
      body.force.x = 0
      body.force.y = 0
    })

    // 检测碰撞
    this.checkCollisions()
  }

  // 检测碰撞
  checkCollisions() {
    const bodies = Array.from(this.bodies.values())
    
    for (let i = 0; i < bodies.length; i++) {
      for (let j = i + 1; j < bodies.length; j++) {
        const bodyA = bodies[i]
        const bodyB = bodies[j]
        
        if (bodyA.isStatic && bodyB.isStatic) continue

        const dx = bodyB.position.x - bodyA.position.x
        const dy = bodyB.position.y - bodyA.position.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        const minDistance = bodyA.radius + bodyB.radius

        if (distance < minDistance) {
          // 碰撞发生
          this.resolveCollision(bodyA, bodyB, dx, dy, distance, minDistance)
        }
      }
    }
  }

  // 解析碰撞
  resolveCollision(bodyA, bodyB, dx, dy, distance, minDistance) {
    if (distance === 0) return

    // 计算分离向量
    const separationX = (dx / distance) * (minDistance - distance) * 0.5
    const separationY = (dy / distance) * (minDistance - distance) * 0.5

    // 分离刚体
    if (!bodyA.isStatic) {
      bodyA.position.x -= separationX
      bodyA.position.y -= separationY
    }
    if (!bodyB.isStatic) {
      bodyB.position.x += separationX
      bodyB.position.y += separationY
    }

    // 计算相对速度
    const relativeVelocityX = bodyB.velocity.x - bodyA.velocity.x
    const relativeVelocityY = bodyB.velocity.y - bodyA.velocity.y

    // 计算法向量
    const normalX = dx / distance
    const normalY = dy / distance

    // 计算相对速度在法向量上的投影
    const velocityAlongNormal = relativeVelocityX * normalX + relativeVelocityY * normalY

    // 如果物体正在分离，不处理碰撞
    if (velocityAlongNormal > 0) return

    // 计算冲量
    const restitution = 0.7 // 弹性系数
    const j = -(1 + restitution) * velocityAlongNormal
    const impulseX = j * normalX
    const impulseY = j * normalY

    // 应用冲量
    if (!bodyA.isStatic) {
      bodyA.velocity.x -= impulseX / bodyA.mass
      bodyA.velocity.y -= impulseY / bodyA.mass
    }
    if (!bodyB.isStatic) {
      bodyB.velocity.x += impulseX / bodyB.mass
      bodyB.velocity.y += impulseY / bodyB.mass
    }

    // 同步位置到水果对象
    bodyA.fruit.x = bodyA.position.x * 30
    bodyA.fruit.y = bodyA.position.y * 30
    bodyB.fruit.x = bodyB.position.x * 30
    bodyB.fruit.y = bodyB.position.y * 30
  }

  // 设置水果位置
  setFruitPosition(fruit, x, y) {
    if (fruit.body) {
      fruit.body.position.x = x / 30
      fruit.body.position.y = y / 30
      fruit.x = x
      fruit.y = y
    }
  }

  // 给水果施加力
  applyForceToFruit(fruit, forceX, forceY) {
    if (fruit.body) {
      fruit.body.force.x += forceX
      fruit.body.force.y += forceY
      fruit.body.isStatic = false
    }
  }

  // 设置合成回调
  setMergeCallback(callback) {
    this.mergeCallback = callback
  }

  // 水果合成回调
  onFruitMerge(fruitA, fruitB) {
    if (this.mergeCallback) {
      this.mergeCallback(fruitA, fruitB)
    }
  }

  // 清理资源
  destroy() {
    this.bodies.clear()
  }
}

// 导出单例
export const simplePhysicsEngine = new SimplePhysicsEngine() 