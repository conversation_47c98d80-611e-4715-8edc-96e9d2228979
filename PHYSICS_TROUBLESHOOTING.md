# 物理引擎故障排除指南

## 问题描述

当遇到 `wx.createPhysicsWorld is not a function` 错误时，说明当前环境不支持微信官方物理引擎。

## 解决方案

### 1. 检查环境支持

当前项目已经配置了自动降级机制：
- 优先尝试使用微信官方物理引擎
- 如果不可用，自动切换到简化物理引擎

### 2. 配置检查

确保 `game.json` 中包含物理引擎配置：
```json
{
  "physics": {
    "enable": true
  }
}
```

### 3. 基础库版本要求

微信官方物理引擎需要：
- 基础库版本 2.30.0 或更高
- 微信开发者工具版本支持物理引擎

### 4. 调试工具使用

在开发环境中，可以使用以下调试命令：

```javascript
// 快速状态检查
checkPhysics()

// 运行完整诊断
runPhysicsDiagnostics()

// 检查物理引擎状态
checkPhysicsStatus()

// 测试角色创建
testFruitCreation()
```

### 5. 手动检查

在控制台中运行：
```javascript
// 检查环境
console.log('wx对象存在:', typeof wx !== 'undefined')
console.log('wx.createPhysicsWorld存在:', typeof wx !== 'undefined' && typeof wx.createPhysicsWorld === 'function')

// 检查系统信息
if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
  const systemInfo = wx.getSystemInfoSync()
  console.log('系统信息:', systemInfo)
}
```

## 当前状态

根据错误日志，项目已经正确实现了降级机制：
1. ✅ 尝试初始化微信物理引擎失败（正常，当前环境不支持）
2. ✅ 自动切换到简化物理引擎（降级机制正常工作）
3. ✅ 简化物理引擎初始化成功
4. ✅ 游戏可以正常运行

**注意**：MIME类型错误是由于动态导入测试文件导致的，已修复。物理引擎本身工作正常。

## 简化物理引擎特性

当使用简化物理引擎时：
- ✅ 重力模拟
- ✅ 碰撞检测
- ✅ 角色下落
- ✅ 基本物理行为
- ⚠️ 性能可能略低于官方引擎
- ⚠️ 物理精度可能略低

## 建议

1. **开发阶段**：使用简化物理引擎进行开发和测试
2. **生产环境**：在支持微信物理引擎的环境中部署
3. **兼容性**：项目已经确保在所有环境下都能正常运行

## 日志说明

正常的工作流程日志：
```
微信物理引擎初始化失败: TypeError: wx.createPhysicsWorld is not a function
微信物理引擎初始化失败，使用简化物理引擎: TypeError: wx.createPhysicsWorld is not a function
物理引擎初始化成功，类型: simple
```

这表明：
1. 微信物理引擎不可用（正常）
2. 自动切换到简化物理引擎（正常）
3. 简化物理引擎初始化成功（正常）

游戏应该可以正常运行，角色会有物理行为。 