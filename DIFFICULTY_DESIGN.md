# 游戏难度设计文档

## 设计目标

参考"羊了个羊"小游戏的难度设计理念，实现以下目标：

- **高难度**：让大部分玩家在富二代阶段（10级）失败
- **15分钟游戏时长**：单局游戏控制在15分钟左右
- **极低通关率**：目标通关率控制在5%以下
- **运气与策略并重**：需要玩家既有策略思考又需要一定运气

## 难度系统架构

### 1. 难度管理器 (DifficultyManager)

位置：`js/managers/DifficultyManager.js`

核心功能：
- 动态难度调整
- 阶段性难度控制
- 恶意生成机制
- 混乱模式管理
- 游戏统计分析

### 2. 四个难度阶段

#### 教学阶段 (TUTORIAL)
- **分数范围**：0-100分
- **特点**：相对简单，让玩家熟悉游戏机制
- **生成权重**：偏向低级球体（小宝50%，年轻哥35%，走读生15%）
- **物理参数**：标准重力和摩擦力

#### 正常阶段 (NORMAL)  
- **分数范围**：100-500分
- **特点**：标准难度，平衡的游戏体验
- **生成权重**：均衡分布（小宝40%，年轻哥35%，走读生20%，大满贯5%）
- **物理参数**：轻微增加难度

#### 挑战阶段 (CHALLENGING)
- **分数范围**：500-2000分
- **特点**：难度明显提升，开始考验玩家技巧
- **生成权重**：增加高级球概率（大满贯15%）
- **物理参数**：重力×1.3，摩擦力降低

#### 噩梦阶段 (NIGHTMARE) - 富二代关卡
- **分数范围**：2000分以上
- **特点**：极高难度，大部分玩家的终点
- **生成权重**：大幅偏向高级球（大满贯25%）
- **物理参数**：重力×2.0，摩擦力大幅降低
- **特殊机制**：开启混乱模式

## 核心难度机制

### 1. 动态物理参数调整

```javascript
// 基于游戏进度的物理参数
LEVEL_MODIFIERS: {
  10: { 
    gravityMultiplier: 1.8,    // 富二代阶段重力大幅增加
    frictionMultiplier: 0.85,  // 摩擦力大幅降低
    instabilityFactor: 1.8     // 不稳定因子增加
  }
}
```

### 2. 时间压力系统

- **启动时间**：游戏进行10分钟后
- **效果**：
  - 重力逐渐增加（每秒+0.002）
  - 摩擦力逐渐降低（每秒-0.001）
  - 最大重力倍数：3.0倍

### 3. 恶意生成机制

#### 恶意连击 (Bad Streak)
- **触发概率**：15%
- **效果**：连续生成3-6个高级球（大满贯）
- **目的**：打破玩家的策略布局

#### 空间压迫生成
- **触发条件**：屏幕70%被占用时
- **效果**：偏向生成大球，增加空间压力
- **倍数**：大球生成概率×2.0

#### 反策略生成
- **机制**：检测玩家的放置模式
- **效果**：30%概率生成反制球体
- **目的**：阻止玩家形成固定策略

### 4. 混乱模式 (Chaos Mode)

仅在噩梦阶段激活：

#### 随机重力突增
- **触发概率**：每帧5%
- **效果**：重力突然增加2.5倍
- **持续时间**：1秒

#### 摩擦力混乱
- **效果**：摩擦力随机波动±30%
- **目的**：增加物理行为的不可预测性

#### 随机扰动
- **效果**：随机对球体施加微小力
- **概率**：每帧0.5%
- **视觉效果**：红色爆炸粒子

## 失败率控制

### 目标指标
- **总体通关率**：5%
- **富二代阶段失败率**：85%
- **平均游戏时长**：12-15分钟

### 自适应难度
- **监控窗口**：最近10局游戏
- **调整触发**：
  - 成功率>30%时增加难度
  - 失败率>95%时降低难度
- **调整幅度**：每次±10%

## UI显示

### 难度信息面板
位置：屏幕右上角

显示内容：
- 当前难度阶段（颜色编码）
- 剩余游戏时间
- 重力倍数（异常时）
- 混乱模式指示（闪烁）
- 警戒线倒计时

### 颜色编码
- 🟢 教学阶段：绿色
- 🔵 正常阶段：蓝色  
- 🟠 挑战阶段：橙色
- 🔴 噩梦阶段：红色

## 平衡性考虑

### 运气因素 (40%)
- 球体生成随机性
- 物理碰撞的微小差异
- 混乱模式的随机事件

### 策略因素 (60%)
- 放置位置选择
- 合成时机把握
- 空间管理能力

## 实现细节

### 关键文件
- `js/config/GameConfig.js` - 难度参数配置
- `js/managers/DifficultyManager.js` - 难度管理核心
- `js/utils/GameData.js` - 数据集成
- `js/scenes/GameScene.js` - 游戏场景集成

### 性能优化
- 难度计算缓存
- 物理参数批量更新
- UI更新频率控制

## 测试验证

### 关键指标监控
- 各阶段平均停留时间
- 失败原因分布
- 玩家重试率
- 最高分分布

### A/B测试建议
- 不同的恶意生成概率
- 混乱模式强度调整
- 时间压力启动时机

---

**注意**：此难度设计旨在创造具有挑战性但公平的游戏体验。所有"恶意"机制都是为了增加游戏的策略深度和重玩价值，而非单纯的挫败玩家。
