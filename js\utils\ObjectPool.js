import { Fruit } from '../objects/Fruit.js'
import { physicsManager } from '../libs/physics-manager.js'

/**
 * 对象池管理器
 * 管理不同类型球体的对象池，提升性能
 */
export class ObjectPool {
  constructor() {
    // 按等级分类的对象池
    this.pools = new Map()
    
    // 默认池大小
    this.defaultPoolSize = 10
    
    // 最大池大小（防止内存泄漏）
    this.maxPoolSize = 50
    
    // 活跃对象统计
    this.activeCount = 0
    this.totalCount = 0
    
    // 对象唯一标识符
    this.nextObjectId = 1
    
    // 活跃对象映射（防止重复激活）
    this.activeObjects = new Map()
  }

  /**
   * 获取指定等级的对象池
   * @param {number} level - 球体等级
   * @returns {Array} 对象池
   */
  getPool(level) {
    if (!this.pools.has(level)) {
      this.pools.set(level, [])
    }
    return this.pools.get(level)
  }

  /**
   * 预创建对象池
   * @param {Array} levels - 需要预创建的等级数组
   * @param {Object} gameData - 游戏数据，用于获取球体配置
   */
  async preCreatePools(levels, gameData) {

    
    for (const level of levels) {
      const pool = this.getPool(level)
      const config = gameData.getFruitConfig(level)
      
      if (!config) {
        console.warn(`未找到等级 ${level} 的配置`)
        continue
      }
      
      // 预创建对象
      for (let i = 0; i < this.defaultPoolSize; i++) {
        const fruit = new Fruit(config, 0, 0, { isPooled: true })
        fruit.poolLevel = level // 标记所属池
        fruit.objectId = this.nextObjectId++ // 分配唯一ID
        fruit.isActive = false // 初始状态为空闲
        
        // 预加载图片（但不创建物理体）
        await fruit.loadImage()
        
        pool.push(fruit)
        this.totalCount++
      }
      

    }
    

  }

  /**
   * 从对象池获取球体
   * @param {number} level - 球体等级
   * @param {number} x - 初始X坐标
   * @param {number} y - 初始Y坐标
   * @param {Object} gameData - 游戏数据
   * @returns {Fruit|null} 球体对象
   */
  async getFruit(level, x, y, gameData) {
    const pool = this.getPool(level)
    
    // 尝试从池中获取空闲对象（同时检查不在活跃映射中）
    let fruit = pool.find(obj => !obj.isActive && !this.activeObjects.has(obj.objectId))
    
    if (!fruit) {
      // 池中没有空闲对象，创建新的
      const config = gameData.getFruitConfig(level)
      if (!config) {
        console.error(`未找到等级 ${level} 的配置`)
        return null
      }
      
      fruit = new Fruit(config, x, y, { isPooled: true })
      fruit.poolLevel = level
      fruit.objectId = this.nextObjectId++ // 分配唯一ID
      this.totalCount++
      

    }
    
    // 最终检查：确保对象确实不在活跃映射中
    if (this.activeObjects.has(fruit.objectId)) {
      console.warn(`对象 ${fruit.objectId} 仍在活跃映射中，跳过激活`)
      return null
    }
    
    // 重置并激活对象
    await this.resetAndActivateFruit(fruit, x, y)
    
    // 添加到活跃对象映射
    this.activeObjects.set(fruit.objectId, fruit)
    this.activeCount++
    

    return fruit
  }

  /**
   * 重置并激活球体
   * @param {Fruit} fruit - 球体对象
   * @param {number} x - 新X坐标
   * @param {number} y - 新Y坐标
   */
  async resetAndActivateFruit(fruit, x, y) {
    // 1. 重置物理属性
    fruit.x = x
    fruit.y = y
    fruit.vx = 0
    fruit.vy = 0
    fruit.angle = 0
    fruit.angularVelocity = 0
    
    // 2. 重置游戏状态
    fruit.isDragging = false
    fruit.isMarkedForMerge = false
    fruit.mergeScale = 1.0 // 重置缩放动画
    fruit.dragStartX = 0
    fruit.dragStartY = 0
    
    // 3. 处理物理体
    if (fruit.body) {
      // 如果已有物理体，重置其状态
      await this.resetPhysicsBody(fruit, x, y)
    } else {
      // 创建新的物理体
      await fruit.createBody()
    }
    
    // 4. 激活对象
    fruit.isActive = true
    

  }

  /**
   * 重置物理体状态
   * @param {Fruit} fruit - 球体对象
   * @param {number} x - 新X坐标
   * @param {number} y - 新Y坐标
   */
  async resetPhysicsBody(fruit, x, y) {
    if (!fruit.body || !physicsManager.isReady()) {
      return
    }
    
    try {
      // 从物理世界中移除
      physicsManager.removeBody(fruit.body)
      
      // 重新创建物理体
      await fruit.createBody()
      
      console.log(`物理体重置: 等级 ${fruit.level}, 新位置(${x}, ${y})`)
    } catch (error) {
      console.error('重置物理体失败:', error)
    }
  }

  /**
   * 回收球体到对象池
   * @param {Fruit} fruit - 要回收的球体
   */
  async recycleFruit(fruit) {
    if (!fruit.isActive || !fruit.isPooled) {
      return
    }
    
    // 1. 从活跃对象映射中移除（必须在重置状态之前）
    if (fruit.objectId && this.activeObjects.has(fruit.objectId)) {
      this.activeObjects.delete(fruit.objectId)

    }
    
    // 2. 从物理世界中移除
    if (fruit.body && physicsManager.isReady()) {
      physicsManager.removeBody(fruit.body)
      fruit.body = null
    }
    
    // 3. 重置状态
    fruit.isActive = false
    fruit.isDragging = false
    fruit.isMarkedForMerge = false
    fruit.mergeScale = 1.0 // 重置缩放动画
    
    // 4. 放回对象池
    const pool = this.getPool(fruit.poolLevel)
    
    // 检查池大小，防止内存泄漏
    if (pool.length < this.maxPoolSize) {
      pool.push(fruit)
      this.activeCount--

    } else {
      // 池已满，直接销毁
      this.totalCount--

    }
  }

  /**
   * 批量回收球体
   * @param {Array} fruits - 要回收的球体数组
   */
  async recycleFruits(fruits) {
    for (const fruit of fruits) {
      await this.recycleFruit(fruit)
    }
  }

  /**
   * 清理所有对象池
   */
  async clearAllPools() {

    
    for (const [level, pool] of this.pools) {
      // 回收所有活跃对象
      const activeFruits = pool.filter(fruit => fruit.isActive)
      await this.recycleFruits(activeFruits)
      
      // 清空池
      pool.length = 0
    }
    
    this.activeCount = 0
    this.totalCount = 0
    this.pools.clear()
    this.activeObjects.clear() // 清理活跃对象映射
    

  }

  /**
   * 获取对象池统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const stats = {
      totalPools: this.pools.size,
      totalObjects: this.totalCount,
      activeObjects: this.activeCount,
      idleObjects: this.totalCount - this.activeCount,
      pools: {}
    }
    
    for (const [level, pool] of this.pools) {
      const activeCount = pool.filter(fruit => fruit.isActive).length
      const idleCount = pool.length - activeCount
      
      stats.pools[level] = {
        total: pool.length,
        active: activeCount,
        idle: idleCount
      }
    }
    
    return stats
  }

  /**
   * 打印对象池状态
   */
  printStats() {
    // 静默模式，不输出任何信息
  }
}

// 创建全局对象池实例
export const objectPool = new ObjectPool() 