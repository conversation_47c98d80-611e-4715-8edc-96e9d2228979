# 开发指南

## JSDoc 类型支持

本项目使用 JSDoc 注释来提供 TypeScript 级别的类型支持，让 IDE 能够提供智能提示和类型检查。

### 类型声明文件

- `js/types/box2d.d.ts` - Box2D 物理引擎的类型声明
- `jsdoc.json` - JSDoc 配置文件

### 使用 JSDoc 的好处

1. **IDE 智能提示**：在编写代码时获得完整的参数和返回值提示
2. **类型检查**：IDE 可以检测类型错误
3. **代码文档**：自动生成 API 文档
4. **重构支持**：IDE 可以安全地重命名和重构代码

### 基本语法

```javascript
/**
 * 函数描述
 * @param {string} param1 - 参数1的描述
 * @param {number} [param2] - 可选参数2的描述
 * @returns {boolean} 返回值的描述
 */
function example(param1, param2 = 0) {
  return true
}

/**
 * 类描述
 */
class ExampleClass {
  /**
   * 构造函数
   * @param {Object} config - 配置对象
   * @param {string} config.name - 名称
   */
  constructor(config) {
    /** @type {string} 实例属性 */
    this.name = config.name
  }
}
```

### 类型定义

```javascript
/**
 * @typedef {Object} FruitConfig
 * @property {number} level - 水果等级
 * @property {string} name - 水果名称
 * @property {string} color - 水果颜色
 * @property {number} radius - 水果半径
 * @property {number} score - 水果得分
 */

/**
 * @param {FruitConfig} config - 水果配置
 */
function createFruit(config) {
  // IDE 会提供 config 属性的智能提示
}
```

### 导入外部类型

```javascript
/**
 * @typedef {import('box2d-wasm')} Box2DModule
 * @typedef {Box2DModule['b2World']} B2World
 */

/** @type {B2World} */
let world = null
```

### 生成文档

```bash
# 安装 JSDoc
npm install -g jsdoc

# 生成文档
jsdoc -c jsdoc.json

# 文档将生成在 docs/ 目录
```

### IDE 配置

#### VS Code

1. 安装 JavaScript 和 TypeScript 扩展
2. 在设置中启用 JSDoc 类型检查：

```json
{
  "js/ts.implicitProjectConfig.checkJs": true,
  "typescript.preferences.includePackageJsonAutoImports": "on"
}
```

#### WebStorm

1. 启用 JavaScript 类型检查
2. 在设置中配置 JSDoc 支持

### 最佳实践

1. **为所有公共方法添加 JSDoc**：包括参数类型和返回值类型
2. **使用类型定义**：对于复杂对象，定义专门的类型
3. **保持文档同步**：当代码变更时，及时更新 JSDoc 注释
4. **使用描述性注释**：不仅仅是类型，还要说明用途和行为

### 示例

```javascript
/**
 * 物理引擎加载器
 * 支持 Box2D WASM 和简化物理引擎两种模式
 */
export class Box2DLoader {
  /**
   * @param {Object} options - 配置选项
   * @param {boolean} [options.useSimplePhysics=true] - 是否使用简化物理引擎
   */
  constructor(options = {}) {
    /** @type {boolean} 是否已加载 */
    this.isLoaded = false
    /** @type {B2World|null} Box2D 物理世界 */
    this.world = null
  }

  /**
   * 创建水果刚体
   * @param {Object} fruit - 水果对象
   * @param {number} fruit.x - 水果X坐标
   * @param {number} fruit.y - 水果Y坐标
   * @param {number} fruit.radius - 水果半径
   * @returns {Object|null} 刚体对象或null
   */
  createFruitBody(fruit) {
    // 实现代码
  }
}
```

### 故障排除

1. **类型不显示**：检查 JSDoc 语法是否正确
2. **导入类型失败**：确保类型声明文件路径正确
3. **IDE 不识别**：重启 IDE 或重新加载项目

通过使用 JSDoc，你可以在纯 JavaScript 项目中获得接近 TypeScript 的开发体验！ 