# 合成大西瓜 - 微信小游戏

一个经典的休闲合成类微信小游戏，包含12个等级的水果合成玩法。

## 游戏特色

- 🍎 **12个等级**：从樱桃到终极西瓜，丰富的合成体验
- 🎮 **简单操作**：点击拖拽即可发射水果
- 🎯 **物理引擎**：真实的物理碰撞和重力效果
- 🎨 **精美画面**：渐变背景和粒子特效
- 🏆 **排行榜**：记录最高分，挑战自我
- 📱 **微信适配**：完美适配微信小游戏平台

## 游戏玩法

1. **发射水果**：点击底部发射区域的水果进行发射
2. **合成升级**：相同等级的水果碰撞后会合成更高级的水果
3. **得分系统**：合成的水果等级越高，得分越多
4. **游戏结束**：当水果堆到顶部时游戏结束
5. **等级提升**：每1000分升一级，解锁更高级的水果

## 水果等级

| 等级 | 水果名称 | 得分 | 颜色 |
|------|----------|------|------|
| 1 | 樱桃 | 10 | 红色 |
| 2 | 草莓 | 20 | 粉红 |
| 3 | 葡萄 | 40 | 紫色 |
| 4 | 橘子 | 80 | 橙色 |
| 5 | 柠檬 | 160 | 黄色 |
| 6 | 猕猴桃 | 320 | 绿色 |
| 7 | 菠萝 | 640 | 橙黄 |
| 8 | 椰子 | 1280 | 棕色 |
| 9 | 西瓜 | 2560 | 绿色 |
| 10 | 大西瓜 | 5120 | 青绿 |
| 11 | 超级西瓜 | 10240 | 蓝色 |
| 12 | 终极西瓜 | 20480 | 红色 |

## 技术特性

- **Canvas渲染**：使用Canvas 2D进行游戏渲染
- **Box2D物理引擎**：真实的物理模拟，包含重力、碰撞、弹跳、滚动
- **事件系统**：完整的触摸事件处理
- **场景管理**：菜单、游戏、结束三个场景
- **音频系统**：完整的音效和背景音乐管理
- **粒子特效**：精美的合成特效和视觉反馈
- **数据持久化**：使用微信存储API保存最高分
- **连锁反应**：支持水果合成的连锁反应机制
- **适配器模式**：兼容微信小游戏环境
- **JSDoc类型支持**：完整的类型声明，提供IDE智能提示

## 项目结构

```
├── game.js                 # 游戏入口文件
├── game.json              # 微信小游戏配置
├── project.config.json    # 项目配置
├── js/
│   ├── main.js            # 游戏主类
│   ├── libs/              # 适配器库
│   │   ├── weapp-adapter.js
│   │   ├── symbol.js
│   │   ├── box2d-wasm.js  # Box2D物理引擎
│   │   └── README.md      # 库文件说明
│   ├── scenes/            # 游戏场景
│   │   ├── MenuScene.js
│   │   ├── GameScene.js
│   │   └── GameOverScene.js
│   ├── objects/           # 游戏对象
│   │   └── Fruit.js
│   ├── utils/             # 工具类
│   │   ├── GameData.js
│   │   ├── AudioManager.js
│   │   ├── ParticleSystem.js
│   │   └── LoadingScreen.js
│   ├── config/            # 配置文件
│   │   └── GameConfig.js
│   ├── test/              # 测试文件
│   │   └── Box2DTest.js
│   └── types/             # 类型声明文件
│       └── box2d.d.ts
├── game_media/            # 游戏资源
│   ├── audio/             # 音频文件
│   └── images/            # 图片资源
└── README.md              # 项目说明
```

## 开发环境

- **微信开发者工具**：用于开发和调试
- **JavaScript ES6+**：使用现代JavaScript语法
- **Canvas API**：2D图形渲染
- **微信小游戏API**：平台特定功能

## 安装和运行

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd watermelon-merge-game
   ```

2. **打开微信开发者工具**
   - 选择"小游戏"项目
   - 导入项目目录
   - 填入你的AppID

3. **配置项目**
   - 在`project.config.json`中修改`appid`为你的小游戏AppID
   - 根据需要调整`game.json`中的配置

4. **运行游戏**
   - 点击预览或真机调试
   - 在微信中体验游戏

## 自定义配置

### 修改游戏参数

在`js/utils/GameData.js`中可以调整：
- 水果配置（颜色、大小、得分）
- 升级规则
- 随机生成算法

### 添加新功能

- **新水果类型**：在`FRUIT_CONFIG`数组中添加配置
- **新特效**：在`GameScene.js`中添加粒子效果
- **新场景**：创建新的场景类并集成到主类中

### 资源替换

将你的音频和图片文件放入`game_media`目录：
- 音频文件：`game_media/audio/`
- 图片文件：`game_media/images/`

## 发布说明

1. **代码优化**
   - 压缩JavaScript代码
   - 优化图片和音频资源
   - 移除调试代码

2. **微信审核**
   - 确保游戏内容符合微信规范
   - 测试各种设备兼容性
   - 准备游戏截图和描述

3. **发布上线**
   - 在微信公众平台提交审核
   - 等待审核通过后发布

## 贡献指南

欢迎提交Issue和Pull Request来改进游戏！

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**享受游戏，快乐合成！** 🍉 