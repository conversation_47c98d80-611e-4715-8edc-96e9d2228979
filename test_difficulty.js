// 难度系统测试脚本
// 用于验证难度管理器的基本功能

// 模拟微信小游戏环境
global.wx = {
  getStorageSync: () => 0,
  setStorageSync: () => {},
  getSystemInfoSync: () => ({ screenWidth: 375, screenHeight: 667 })
}

// 导入需要测试的模块
import { GameConfig } from './js/config/GameConfig.js'
import { DifficultyManager } from './js/managers/DifficultyManager.js'

// 模拟GameData
class MockGameData {
  constructor() {
    this.score = 0
    this.fruits = []
    this.gameStartTime = Date.now()
  }
}

// 测试函数
function testDifficultySystem() {
  console.log('🧪 开始测试难度系统...\n')
  
  // 创建测试实例
  const gameData = new MockGameData()
  const difficultyManager = new DifficultyManager(gameData)
  
  // 测试1: 初始状态
  console.log('📋 测试1: 初始状态')
  console.log(`当前阶段: ${difficultyManager.currentPhase}`)
  console.log(`重力倍数: ${difficultyManager.getGravityMultiplier()}`)
  console.log(`摩擦力倍数: ${difficultyManager.getFrictionMultiplier()}`)
  console.log(`不稳定因子: ${difficultyManager.getInstabilityFactor()}`)
  console.log('')
  
  // 测试2: 阶段切换
  console.log('📋 测试2: 阶段切换')
  
  // 模拟分数增长
  const testScores = [50, 150, 800, 2500]
  const expectedPhases = ['TUTORIAL', 'NORMAL', 'CHALLENGING', 'NIGHTMARE']
  
  testScores.forEach((score, index) => {
    gameData.score = score
    difficultyManager.updateGamePhase()
    
    console.log(`分数: ${score} -> 阶段: ${difficultyManager.currentPhase}`)
    
    if (difficultyManager.currentPhase === expectedPhases[index]) {
      console.log('✅ 阶段切换正确')
    } else {
      console.log(`❌ 阶段切换错误，期望: ${expectedPhases[index]}`)
    }
  })
  console.log('')
  
  // 测试3: 生成权重
  console.log('📋 测试3: 生成权重测试')
  
  testScores.forEach(score => {
    gameData.score = score
    difficultyManager.updateGamePhase()
    
    const weights = difficultyManager.getGenerationWeights()
    console.log(`分数: ${score}, 阶段: ${difficultyManager.currentPhase}`)
    console.log(`权重分布:`, weights)
    
    // 验证权重总和
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0)
    console.log(`权重总和: ${totalWeight}`)
    console.log('')
  })
  
  // 测试4: 恶意生成机制
  console.log('📋 测试4: 恶意生成机制')
  
  // 重置到噩梦阶段
  gameData.score = 3000
  difficultyManager.updateGamePhase()
  
  // 测试多次生成，看是否触发恶意机制
  let badStreakCount = 0
  let spacePressureCount = 0
  
  for (let i = 0; i < 100; i++) {
    const weights = difficultyManager.getGenerationWeights()
    
    // 检查是否触发恶意连击（权重集中在单一等级）
    const maxWeight = Math.max(...Object.values(weights))
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0)
    
    if (maxWeight === 100 && totalWeight === 100) {
      badStreakCount++
    }
    
    // 模拟空间压迫（增加水果数量）
    if (i > 50) {
      gameData.fruits = new Array(15).fill({}) // 模拟15个水果
    }
  }
  
  console.log(`恶意连击触发次数: ${badStreakCount}/100`)
  console.log(`空间压迫测试完成`)
  console.log('')
  
  // 测试5: 时间压力
  console.log('📋 测试5: 时间压力测试')
  
  // 模拟游戏进行10分钟
  const originalStartTime = gameData.gameStartTime
  gameData.gameStartTime = Date.now() - 11 * 60 * 1000 // 11分钟前
  
  difficultyManager.update(16.67) // 模拟一帧更新
  
  console.log(`时间压力激活: ${difficultyManager.timePressureActive}`)
  console.log(`动态重力倍数: ${difficultyManager.dynamicGravityMultiplier.toFixed(3)}`)
  console.log(`动态摩擦力倍数: ${difficultyManager.dynamicFrictionMultiplier.toFixed(3)}`)
  console.log('')
  
  // 测试6: 混乱模式
  console.log('📋 测试6: 混乱模式测试')
  
  // 确保在噩梦阶段
  gameData.score = 5000
  difficultyManager.updateGamePhase()
  
  console.log(`混乱模式激活: ${difficultyManager.chaosMode}`)
  
  // 模拟多次更新，测试重力突增
  let gravitySpikes = 0
  for (let i = 0; i < 1000; i++) {
    difficultyManager.updateChaosMode(16.67)
    if (difficultyManager.gravitySpike.active) {
      gravitySpikes++
    }
  }
  
  console.log(`重力突增触发次数: ${gravitySpikes}/1000`)
  console.log('')
  
  // 测试7: 配置验证
  console.log('📋 测试7: 配置验证')
  
  // 验证配置完整性
  const config = GameConfig.DIFFICULTY
  
  console.log('难度等级配置:')
  Object.keys(config.LEVEL_MODIFIERS).forEach(level => {
    const modifier = config.LEVEL_MODIFIERS[level]
    console.log(`等级${level}: 重力×${modifier.gravityMultiplier}, 摩擦×${modifier.frictionMultiplier}, 不稳定×${modifier.instabilityFactor}`)
  })
  
  console.log('\n阶段配置:')
  Object.keys(config.SHEEP_DIFFICULTY.PHASES).forEach(phase => {
    const phaseConfig = config.SHEEP_DIFFICULTY.PHASES[phase]
    console.log(`${phase}: 分数${phaseConfig.scoreRange[0]}-${phaseConfig.scoreRange[1]}, 难度×${phaseConfig.difficultyMultiplier}`)
  })
  
  console.log('\n✅ 难度系统测试完成!')
}

// 运行测试
if (typeof window === 'undefined') {
  // Node.js环境
  testDifficultySystem()
} else {
  // 浏览器环境
  console.log('请在Node.js环境中运行此测试脚本')
}
