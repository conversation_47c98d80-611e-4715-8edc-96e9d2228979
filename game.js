import './js/libs/weapp-adapter'
import './js/libs/symbol'

// 直接导入Matter.js，让它自动挂载到全局
import './js/libs/matter.min.js'

// 确保Matter.js在全局可用
if (typeof window !== 'undefined' && typeof Matter !== 'undefined') {
  window.Matter = Matter
  console.log('✅ Matter.js 已设置到全局作用域')
} else if (typeof global !== 'undefined' && typeof Matter !== 'undefined') {
  global.Matter = Matter
  console.log('✅ Matter.js 已设置到全局作用域')
}

import Main from './js/main'

new Main() 