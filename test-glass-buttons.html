<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>玻璃拟态按钮测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        canvas {
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .control-button {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            color: white;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <h1>🎮 玻璃拟态按钮效果测试</h1>
    <canvas id="testCanvas" width="800" height="600"></canvas>
    
    <div class="controls">
        <button class="control-button" onclick="toggleStyle()">切换风格</button>
        <button class="control-button" onclick="toggleHover()">切换悬停</button>
        <button class="control-button" onclick="animateButtons()">动画效果</button>
    </div>

    <script src="js/config/Y2KUIConfig.js"></script>
    <script src="js/utils/PixelEffects.js"></script>
    
    <script>
        class GlassButtonTest {
            constructor() {
                this.canvas = document.getElementById('testCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.pixelEffects = new PixelEffects(this.ctx, this.canvas);
                
                this.isGlassStyle = true;
                this.isHovered = false;
                this.animationFrame = 0;
                
                this.buttons = [
                    {
                        x: 200, y: 150,
                        width: 160, height: 50,
                        text: '开始游戏',
                        color: Y2KUIConfig.COLORS.ELECTRIC_BLUE
                    },
                    {
                        x: 600, y: 150,
                        width: 160, height: 50,
                        text: '设置选项',
                        color: Y2KUIConfig.COLORS.MATRIX_GREEN
                    },
                    {
                        x: 200, y: 250,
                        width: 160, height: 50,
                        text: '游戏记录',
                        color: Y2KUIConfig.COLORS.NEON_PINK
                    },
                    {
                        x: 600, y: 250,
                        width: 160, height: 50,
                        text: '退出游戏',
                        color: Y2KUIConfig.COLORS.CYBER_ORANGE
                    },
                    {
                        x: 400, y: 350,
                        width: 200, height: 60,
                        text: '特殊按钮',
                        color: Y2KUIConfig.COLORS.HOLOGRAM_PURPLE
                    }
                ];
                
                this.init();
            }
            
            init() {
                this.render();
                this.animate();
            }
            
            render() {
                // 清空画布
                this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 绘制标题
                this.ctx.font = '32px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('玻璃拟态按钮展示', this.canvas.width / 2, 50);
                
                // 绘制风格说明
                this.ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                this.ctx.fillText(
                    `当前风格: ${this.isGlassStyle ? '玻璃拟态' : '像素风格'} | 悬停状态: ${this.isHovered ? '开启' : '关闭'}`,
                    this.canvas.width / 2, 80
                );
                
                // 绘制按钮
                this.buttons.forEach((button, index) => {
                    const config = {
                        color: button.color,
                        isHovered: this.isHovered,
                        glassStyle: this.isGlassStyle
                    };
                    
                    // 绘制按钮
                    this.pixelEffects.drawPixelButton(
                        button.x, button.y,
                        button.width, button.height,
                        config
                    );
                    
                    // 绘制按钮文字
                    this.pixelEffects.drawPixelText(
                        button.text,
                        button.x, button.y,
                        Y2KUIConfig.FONTS.PIXEL_BUTTON,
                        'rgba(255, 255, 255, 0.9)',
                        null,
                        this.isGlassStyle,
                        this.isHovered
                    );
                });
                
                // 绘制说明文字
                this.ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                this.ctx.fillText('使用下方按钮控制效果展示', this.canvas.width / 2, this.canvas.height - 30);
            }
            
            animate() {
                this.animationFrame++;
                this.render();
                requestAnimationFrame(() => this.animate());
            }
        }
        
        let test;
        
        window.onload = () => {
            test = new GlassButtonTest();
        };
        
        function toggleStyle() {
            test.isGlassStyle = !test.isGlassStyle;
        }
        
        function toggleHover() {
            test.isHovered = !test.isHovered;
        }
        
        function animateButtons() {
            // 简单的动画效果
            test.buttons.forEach((button, index) => {
                setTimeout(() => {
                    test.isHovered = true;
                    setTimeout(() => {
                        test.isHovered = false;
                    }, 200);
                }, index * 100);
            });
        }
    </script>
</body>
</html>
