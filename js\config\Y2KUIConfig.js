/**
 * Y2K/90年代网络美学UI配置
 * 基于赛博朋克与复古未来主义的视觉设计系统
 */

export const Y2KUIConfig = {
  // 核心色彩系统 - 高饱和度电子色彩
  COLORS: {
    // 主色调
    CYBER_CYAN: '#00FFFF',      // 电子蓝 - 主要交互元素
    MATRIX_GREEN: '#00FF41',    // 矩阵绿 - 成功状态
    PIXEL_BLACK: '#0A0A0A',     // 像素黑 - 背景
    DIGITAL_WHITE: '#FFFFFF',   // 数字白 - 高对比度文字
    
    // 辅助色彩
    NEON_PINK: '#FF0080',       // 霓虹粉 - 警告/危险
    METAL_SILVER: '#C0C0C0',    // 金属银 - 装饰元素
    ELECTRIC_PURPLE: '#8A2BE2', // 电紫 - 特殊效果
    LIQUID_ORANGE: '#FF6600',   // 液态橙 - 次要按钮
    
    // 渐变色组合
    GRADIENTS: {
      CYBER_BLUE: ['#00FFFF', '#0080FF'],
      MATRIX_FLOW: ['#00FF41', '#008F11'],
      NEON_GLOW: ['#FF0080', '#8A2BE2'],
      METAL_SHINE: ['#C0C0C0', '#808080'],
      DIGITAL_FADE: ['#FFFFFF', '#808080']
    },
    
    // 透明度变体
    ALPHA: {
      BACKGROUND_OVERLAY: 'rgba(10, 10, 10, 0.85)',
      GLOW_EFFECT: 'rgba(0, 255, 255, 0.3)',
      SCAN_LINE: 'rgba(255, 255, 255, 0.1)',
      SHADOW: 'rgba(0, 0, 0, 0.6)'
    }
  },

  // 字体系统 - 像素化字体层级
  FONTS: {
    // 像素字体配置
    PIXEL_TITLE: {
      size: 48,
      weight: 'bold',
      family: 'monospace',
      pixelated: true,
      strokeWidth: 2
    },
    
    PIXEL_BUTTON: {
      size: 24,
      weight: 'bold', 
      family: 'monospace',
      pixelated: true,
      strokeWidth: 1
    },
    
    DIGITAL_SCORE: {
      size: 32,
      weight: 'bold',
      family: 'monospace',
      pixelated: true,
      strokeWidth: 1
    },
    
    SYSTEM_HINT: {
      size: 16,
      weight: 'normal',
      family: 'monospace',
      pixelated: true,
      strokeWidth: 1
    },
    
    GLITCH_TEXT: {
      size: 20,
      weight: 'bold',
      family: 'monospace',
      pixelated: true,
      strokeWidth: 1,
      glitchEffect: true
    }
  },

  // UI元素尺寸规范
  DIMENSIONS: {
    // 按钮尺寸
    BUTTON_PRIMARY: { width: 280, height: 70 },
    BUTTON_SECONDARY: { width: 200, height: 50 },
    BUTTON_ICON: { width: 50, height: 50 },
    
    // 面板尺寸
    MODAL_PANEL: { width: 420, height: 550 }, // 增大弹窗尺寸
    SCORE_PANEL: { width: 200, height: 80 },
    MENU_PANEL: { width: 300, height: 400 },
    
    // 边框和间距
    BORDER_WIDTH: 2,
    PIXEL_BORDER: 3,
    ELEMENT_SPACING: 20,
    PANEL_PADDING: 25
  },

  // 动画效果配置
  ANIMATIONS: {
    // 按钮动画
    BUTTON_HOVER: {
      duration: 200,
      easing: 'ease-out',
      scale: 1.05,
      glowIntensity: 0.8
    },
    
    BUTTON_CLICK: {
      duration: 100,
      easing: 'ease-in',
      scale: 0.95,
      compressionEffect: true
    },
    
    // 文字效果
    TYPEWRITER: {
      charDelay: 50,
      cursorBlink: 500
    },
    
    GLITCH: {
      frequency: 0.0, // 禁用故障效果
      intensity: 0.0,
      duration: 0
    },
    
    // 扫描线效果
    SCAN_LINES: {
      speed: 2,
      opacity: 0.1,
      spacing: 4
    },
    
    // 脉冲效果
    PULSE: {
      duration: 1000,
      minScale: 1.0,
      maxScale: 1.1
    }
  },

  // 视觉效果配置
  EFFECTS: {
    // 像素化效果
    PIXELATION: {
      enabled: true,
      pixelSize: 2,
      antiAliasing: false
    },
    
    // CRT显示器效果
    CRT_EFFECT: {
      scanLines: true,
      curvature: 0.1,
      vignette: 0.3,
      phosphorGlow: true
    },
    
    // 故障效果 - 已禁用
    GLITCH_EFFECT: {
      rgbShift: 0,
      noiseIntensity: 0.0,
      blockSize: 0
    },
    
    // 光晕效果
    GLOW_EFFECT: {
      blurRadius: 10,
      intensity: 0.6,
      color: '#00FFFF'
    }
  },

  // 装饰元素配置
  DECORATIONS: {
    // 几何装饰
    GEOMETRIC_SHAPES: {
      triangles: true,
      hexagons: true,
      circuits: true,
      pixelNoise: true
    },
    
    // 赛博朋克符号
    CYBER_SYMBOLS: [
      '◢', '◣', '◤', '◥',  // 三角形
      '▲', '▼', '◆', '◇',  // 基础几何
      '⬢', '⬡', '⬟', '⬠',  // 六边形
      '▓', '▒', '░',        // 像素块
      '┌', '┐', '└', '┘',   // 边框角
      '├', '┤', '┬', '┴'    // 连接符
    ],
    
    // 抽象装饰
    ABSTRACT_ELEMENTS: {
      pixelClouds: true,
      digitalStars: true,
      circuitLines: true,
      dataStreams: true
    }
  },

  // 布局配置
  LAYOUT: {
    // 安全区域
    SAFE_AREA: {
      top: 40,
      bottom: 40,
      left: 20,
      right: 20
    },
    
    // 网格系统
    GRID: {
      columns: 12,
      gutterWidth: 20,
      containerMaxWidth: 400
    },
    
    // Z-index层级
    Z_INDEX: {
      BACKGROUND: 0,
      DECORATIONS: 10,
      UI_ELEMENTS: 20,
      MODALS: 30,
      TOOLTIPS: 40,
      EFFECTS: 50
    }
  },

  // 响应式断点
  BREAKPOINTS: {
    SMALL: 320,   // 小屏手机
    MEDIUM: 375,  // 标准手机
    LARGE: 414,   // 大屏手机
    XLARGE: 480   // 平板竖屏
  },

  // 可访问性配置
  ACCESSIBILITY: {
    MIN_CONTRAST_RATIO: 4.5,
    MIN_TOUCH_TARGET: 44,
    FOCUS_OUTLINE_WIDTH: 3,
    FOCUS_OUTLINE_COLOR: '#00FFFF'
  },

  // 响应式缩放配置
  RESPONSIVE: {
    // 基准屏幕宽度
    BASE_WIDTH: 375,
    // 最小缩放比例
    MIN_SCALE: 0.8,
    // 最大缩放比例
    MAX_SCALE: 1.2,
    // 字体缩放因子
    FONT_SCALE_FACTOR: 0.9,
    // 按钮最小尺寸
    MIN_BUTTON_SIZE: 40
  }
}

// 工具函数：获取像素化字体样式
export function getPixelFont(config) {
  return `${config.weight} ${config.size}px ${config.family}`
}

// 工具函数：创建渐变
export function createGradient(ctx, gradient, x1, y1, x2, y2) {
  const grad = ctx.createLinearGradient(x1, y1, x2, y2)
  gradient.forEach((color, index) => {
    grad.addColorStop(index / (gradient.length - 1), color)
  })
  return grad
}

// 工具函数：应用像素化效果
export function applyPixelation(ctx, pixelSize = 2) {
  ctx.imageSmoothingEnabled = false
  ctx.webkitImageSmoothingEnabled = false
  ctx.mozImageSmoothingEnabled = false
  ctx.msImageSmoothingEnabled = false
}

// 工具函数：绘制像素化边框
export function drawPixelBorder(ctx, x, y, width, height, color, borderWidth = 2) {
  ctx.fillStyle = color
  // 上边框
  ctx.fillRect(x, y, width, borderWidth)
  // 下边框  
  ctx.fillRect(x, y + height - borderWidth, width, borderWidth)
  // 左边框
  ctx.fillRect(x, y, borderWidth, height)
  // 右边框
  ctx.fillRect(x + width - borderWidth, y, borderWidth, height)
}

// 工具函数：绘制故障文字效果
export function drawGlitchText(ctx, text, x, y, font, color, glitchIntensity = 0.3) {
  ctx.font = font
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'

  // 主文字
  ctx.fillStyle = color
  ctx.fillText(text, x, y)

  // 故障效果
  if (Math.random() < glitchIntensity) {
    ctx.fillStyle = Y2KUIConfig.COLORS.NEON_PINK
    ctx.fillText(text, x + Math.random() * 4 - 2, y)

    ctx.fillStyle = Y2KUIConfig.COLORS.CYBER_CYAN
    ctx.fillText(text, x + Math.random() * 4 - 2, y)
  }
}

// 工具函数：获取响应式缩放比例
export function getResponsiveScale(screenWidth) {
  const scale = screenWidth / Y2KUIConfig.RESPONSIVE.BASE_WIDTH
  return Math.max(
    Y2KUIConfig.RESPONSIVE.MIN_SCALE,
    Math.min(Y2KUIConfig.RESPONSIVE.MAX_SCALE, scale)
  )
}

// 工具函数：获取响应式字体大小
export function getResponsiveFontSize(baseFontSize, screenWidth) {
  const scale = getResponsiveScale(screenWidth)
  return Math.max(12, Math.floor(baseFontSize * scale * Y2KUIConfig.RESPONSIVE.FONT_SCALE_FACTOR))
}

// 工具函数：获取响应式按钮尺寸
export function getResponsiveButtonSize(baseWidth, baseHeight, screenWidth) {
  const scale = getResponsiveScale(screenWidth)
  return {
    width: Math.max(Y2KUIConfig.RESPONSIVE.MIN_BUTTON_SIZE, Math.floor(baseWidth * scale)),
    height: Math.max(Y2KUIConfig.RESPONSIVE.MIN_BUTTON_SIZE, Math.floor(baseHeight * scale))
  }
}

// 工具函数：检查设备类型
export function getDeviceType(screenWidth) {
  if (screenWidth <= Y2KUIConfig.BREAKPOINTS.SMALL) {
    return 'small'
  } else if (screenWidth <= Y2KUIConfig.BREAKPOINTS.MEDIUM) {
    return 'medium'
  } else if (screenWidth <= Y2KUIConfig.BREAKPOINTS.LARGE) {
    return 'large'
  } else {
    return 'xlarge'
  }
}

// 工具函数：获取设备特定的UI配置
export function getDeviceSpecificConfig(screenWidth) {
  const deviceType = getDeviceType(screenWidth)
  const scale = getResponsiveScale(screenWidth)

  return {
    deviceType,
    scale,
    buttonPadding: Math.max(10, Math.floor(15 * scale)),
    textPadding: Math.max(5, Math.floor(10 * scale)),
    borderWidth: Math.max(1, Math.floor(2 * scale)),
    iconSize: Math.max(16, Math.floor(24 * scale))
  }
}
