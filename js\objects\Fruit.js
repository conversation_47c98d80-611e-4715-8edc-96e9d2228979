import { physicsManager } from '../libs/physics-manager.js'

/**
 * 角色类
 * 表示游戏中的角色对象，包含物理属性和渲染逻辑
 */
export class Fruit {
  /**
   * @param {Object} config - 角色配置
   * @param {number} config.level - 角色等级
   * @param {string} config.name - 角色名称
   * @param {string} config.color - 角色颜色
   * @param {number} config.radius - 角色半径
   * @param {number} config.score - 角色得分
   * @param {number} x - 初始X坐标
   * @param {number} y - 初始Y坐标
   */
  constructor(config, x, y, options = {}) {
    this.level = config.level
    this.name = config.name
    this.color = config.color
    this.radius = config.radius
    this.score = config.score
    
    // 特殊角色属性
    this.isSpecial = config.isSpecial || false
    this.isUltimate = config.isUltimate || false
    
    // 图片属性
    this.imagePath = config.imagePath
    this.image = null
    this.imageLoaded = false
    
    // 物理属性 - 用户新需求
    this.x = x
    this.y = y
    this.vx = 0
    this.vy = 0
    this.gravity = 2 // 更快下落
    this.friction = 0.95 // 减少摩擦力，让球体更滑溜
    this.bounce = 0.3 // 增加弹性，让球体更Q弹
    this.linearDamping = 0.03 // 减少线性阻尼，让球体更有活力
    
    /** @type {Object|null} Box2D 刚体 */
    this.body = null
    
    /** @type {boolean} 是否静止 */
    this.isStatic = false
    /** @type {boolean} 是否正在拖拽 */
    this.isDragging = false
    /** @type {number} 拖拽开始X坐标 */
    this.dragStartX = 0
    /** @type {number} 拖拽开始Y坐标 */
    this.dragStartY = 0
    /** @type {boolean} 标记是否等待合成 */
    this.isMarkedForMerge = false
    
    /** @type {number} 碰撞检测半径 */
    this.collisionRadius = this.radius + 2
    
    // 新增：是否为装饰性角色
    this.isDecorative = options.isDecorative || false
    
    // 对象池相关属性
    this.isPooled = options.isPooled || false
    this.isActive = false
    this.poolLevel = null
    
    // 加载图片
    this.loadImage()
  }
  
  /**
   * 加载角色头像图片
   */
  loadImage() {
    if (!this.imagePath) return
    
    // 尝试从全局ResourceLoader获取已加载的图片
    if (window.gameMain && window.gameMain.resourceLoader) {
      const loadedImage = window.gameMain.resourceLoader.getLoadedImage(this.imagePath)
      if (loadedImage) {
        this.image = loadedImage
        this.imageLoaded = true
        return
      }
    }
    
    // 如果ResourceLoader中没有，则创建新图片
    try {
      this.image = wx.createImage()
      this.image.onload = () => {
        this.imageLoaded = true
        console.log(`图片加载成功: ${this.imagePath}`)
      }
      this.image.onerror = (error) => {
        console.warn(`图片加载失败: ${this.imagePath}`, error)
        this.imageLoaded = false
        // 图片加载失败不影响物理更新
      }
      this.image.src = this.imagePath
    } catch (error) {
      console.warn(`创建图片对象失败: ${this.imagePath}`, error)
      this.imageLoaded = false
      // 图片创建失败不影响物理更新
    }
  }
  
  /**
   * 异步创建物理刚体
   */
  async createBody() {
    try {
      if (this.isDecorative) {
        console.log(`角色 ${this.name} 为装饰性角色，跳过物理刚体创建`)
        this.body = null
        return
      }
      

      
      // 使用Matter.js创建圆形物理体
      this.body = physicsManager.createCircleBody(this.x, this.y, this.radius, {
        restitution: this.bounce, // 使用更高的弹性
        friction: 0.05, // 减少摩擦力，让球体更滑溜
        density: 0.001,
        label: this.name
      })
      

    } catch (error) {
      console.warn(`角色 ${this.name} 物理刚体创建失败:`, error)
      this.body = null
    }
  }
  
  /**
   * 更新角色状态
   */
  update() {
    if (this.isStatic) return
    
    // 如果有Matter.js物理体，从物理体获取位置和速度
    if (this.body) {
      const oldX = this.x
      const oldY = this.y
      
      this.x = this.body.position.x
      this.y = this.body.position.y
      this.vx = this.body.velocity.x
      this.vy = this.body.velocity.y
      

      
      // 检查位置是否发生变化
      
      
      // 检查是否静止
      if (Math.abs(this.vx) < 0.1 && Math.abs(this.vy) < 0.1) {
        this.isStatic = true
      }
    } else {
      // 兼容简化物理引擎
      // 确保物理参数正常
      if (typeof this.gravity !== 'number' || this.gravity <= 0) {
        this.gravity = 2 // 重置重力
      }
      if (typeof this.friction !== 'number' || this.friction <= 0) {
        this.friction = 0.97 // 重置摩擦力
      }
      if (typeof this.linearDamping !== 'number' || this.linearDamping <= 0) {
        this.linearDamping = 0.05 // 重置线性阻尼
      }
    
    // 应用重力
    this.vy += this.gravity
    
    // 应用线性阻尼，快速消耗动能
    this.vx *= (1 - this.linearDamping * 0.1)
    this.vy *= (1 - this.linearDamping * 0.1)
    
    // 应用摩擦力
    this.vx *= this.friction
    this.vy *= this.friction
    
    // 更新位置
    this.x += this.vx
    this.y += this.vy
    
    // 检查边界碰撞
    this.checkBoundaryCollision()
    
    // 如果速度很小且接近底部，设为静态
    const screenHeight = wx.getSystemInfoSync().screenHeight
    if (Math.abs(this.vx) < 0.05 && Math.abs(this.vy) < 0.05 && this.y + this.radius >= screenHeight - 5) {
      this.isStatic = true
      this.vx = 0
      this.vy = 0
      }
    }
  }
  
  /**
   * 检查是否点击了特殊角色（丐帮主）
   * @param {number} clickX - 点击X坐标
   * @param {number} clickY - 点击Y坐标
   * @returns {boolean} 是否点击了特殊角色
   */
  checkSpecialClick(clickX, clickY) {
    if (!this.isSpecial) return false
    
    const distance = Math.sqrt(
      Math.pow(clickX - this.x, 2) + Math.pow(clickY - this.y, 2)
    )
    
    return distance <= this.radius
  }
  
  /**
   * 处理特殊角色点击事件
   * @returns {number} 返回奖励分数
   */
  handleSpecialClick() {
    if (!this.isSpecial) return 0
    
    // 丐帮主点击奖励666分
    if (this.name === '丐帮主') {
      return 666
    }
    
    return 0
  }
  
  /**
   * 检查边界碰撞
   */
  checkBoundaryCollision() {
    const screenWidth = wx.getSystemInfoSync().screenWidth
    const screenHeight = wx.getSystemInfoSync().screenHeight
    
    // 左右边界 - 无弹跳
    if (this.x - this.radius <= 0) {
      this.x = this.radius
      this.vx = 0
    } else if (this.x + this.radius >= screenWidth) {
      this.x = screenWidth - this.radius
      this.vx = 0
    }
    
    // 底部边界 - 无弹跳
    if (this.y + this.radius >= screenHeight) {
      this.y = screenHeight - this.radius
      this.vy = 0
      
      // 如果速度很小，设为静态
      if (Math.abs(this.vy) < 0.3 && Math.abs(this.vx) < 0.3) {
        this.vy = 0
        this.vx = 0
        this.isStatic = true
      }
    }
    
    // 顶部边界 - 无弹跳
    if (this.y - this.radius <= 0) {
      this.y = this.radius
      this.vy = 0
    }
  }
  
  /**
   * 销毁角色对象
   */
  destroy() {
    // 移除Matter.js物理体
    if (this.body) {
      physicsManager.removeBody(this.body)
      this.body = null
    }
  }
  
  /**
   * 检查与其他角色的碰撞
   * @param {Fruit} other - 其他角色对象
   * @returns {boolean} 是否发生碰撞
   */
  checkCollision(other) {
    const dx = this.x - other.x
    const dy = this.y - other.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    const minDistance = this.radius + other.radius
    
    return distance < minDistance
  }
  
  /**
   * 处理与其他角色的碰撞
   * @param {Fruit} other - 其他角色对象
   */
  handleCollision(other) {
    const dx = this.x - other.x
    const dy = this.y - other.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    const minDistance = this.radius + other.radius
    
    if (distance < minDistance) {
      // 计算需要分离的距离
      const overlap = minDistance - distance
      const nx = dx / distance
      const ny = dy / distance
      // 平分分离量，确保相切
      const separation = overlap / 2
      this.x += nx * separation
      this.y += ny * separation
      other.x -= nx * separation
      other.y -= ny * separation
      
      // 计算碰撞后的速度（弹性碰撞）
      const normalX = nx
      const normalY = ny
      
      // 计算相对速度
      const relativeVelocityX = this.vx - other.vx
      const relativeVelocityY = this.vy - other.vy
      
      // 计算速度在法线方向的分量
      const velocityAlongNormal = relativeVelocityX * normalX + relativeVelocityY * normalY
      
      // 如果角色正在分离，不进行速度交换
      if (velocityAlongNormal > 0) return
      
      // 计算冲量
      const restitution = 0 // 无弹性
      const impulse = -(1 + restitution) * velocityAlongNormal
      
      // 应用冲量
      const impulseX = impulse * normalX
      const impulseY = impulse * normalY
      
      this.vx += impulseX
      this.vy += impulseY
      other.vx -= impulseX
      other.vy -= impulseY
      
      // 限制碰撞后的速度，防止过大的冲击
      const maxSpeed = 5
      this.vx = Math.max(-maxSpeed, Math.min(maxSpeed, this.vx))
      this.vy = Math.max(-maxSpeed, Math.min(maxSpeed, this.vy))
      other.vx = Math.max(-maxSpeed, Math.min(maxSpeed, other.vx))
      other.vy = Math.max(-maxSpeed, Math.min(maxSpeed, other.vy))
      
      // 增加摩擦力，快速消耗动能
      this.vx *= 0.9
      this.vy *= 0.9
      other.vx *= 0.9
      other.vy *= 0.9
    }
  }
  
  /**
   * 开始拖拽
   * @param {number} x - 拖拽开始X坐标
   * @param {number} y - 拖拽开始Y坐标
   */
  startDrag(x, y) {
    this.isDragging = true
    this.dragStartX = x
    this.dragStartY = y
    this.isStatic = false
    
    // 物理引擎由 GameScene 统一管理
    if (this.body) {
      this.body.isStatic = true
    }
  }
  
  /**
   * 更新拖拽位置
   * @param {number} x - 当前X坐标
   * @param {number} y - 当前Y坐标
   */
  updateDrag(x, y) {
    if (!this.isDragging) return
    
    this.x = x
    this.y = y
    
    // 物理引擎由 GameScene 统一管理
    if (this.body) {
      this.body.position.x = x / 30
      this.body.position.y = y / 30
    }
  }
  
  /**
   * 结束拖拽
   */
  endDrag() {
    if (!this.isDragging) return
    
    this.isDragging = false
    
    // 计算发射速度
    const dragDistance = Math.sqrt(
      Math.pow(this.x - this.dragStartX, 2) + 
      Math.pow(this.y - this.dragStartY, 2)
    )
    
    if (dragDistance > 10) {
      const angle = Math.atan2(this.y - this.dragStartY, this.x - this.dragStartX)
      const speed = Math.min(dragDistance * 0.1, 15)
      
      // 物理引擎由 GameScene 统一管理
      if (this.body) {
        this.body.isStatic = false
        const forceX = Math.cos(angle) * speed * 10
        const forceY = Math.sin(angle) * speed * 10
        this.body.force.x += forceX
        this.body.force.y += forceY
      } else {
        this.vx = Math.cos(angle) * speed
        this.vy = Math.sin(angle) * speed
      }
    } else {
      // 恢复为动态刚体
      if (this.body) {
        this.body.isStatic = false
      }
    }
  }
  
  /**
   * 渲染角色
   * @param {CanvasRenderingContext2D} ctx - Canvas 2D 上下文
   */
  render(ctx) {
    ctx.save()
    
    // 应用合成缩放动画
    if (this.mergeScale && this.mergeScale !== 1.0) {
      ctx.translate(this.x, this.y)
      ctx.scale(this.mergeScale, this.mergeScale)
      ctx.translate(-this.x, -this.y)
      
      // 更新缩放动画
      if (this.mergeScale > this.mergeScaleTarget) {
        this.mergeScale = Math.max(this.mergeScaleTarget, this.mergeScale - this.mergeScaleSpeed)
      }
    }
    
            // 绘制角色阴影
        ctx.fillStyle = 'rgba(0, 0, 0, 0.2)'
        ctx.beginPath()
        ctx.arc(this.x + 3, this.y + 3, this.radius, 0, Math.PI * 2)
        ctx.fill()
    
    // 如果有图片且已加载，绘制图片
    if (this.image && this.imageLoaded) {
      // 绘制圆形裁剪区域
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
      ctx.clip()
      
      // 计算图片绘制参数 - 保持宽高比
      const imageSize = this.radius * 1.8 // 增大头像尺寸
      const imageX = this.x - this.radius * 0.9
      const imageY = this.y - this.radius * 0.9
      
      // 根据图片宽高比调整绘制尺寸
      const imageAspectRatio = this.image.naturalWidth / this.image.naturalHeight
      let drawWidth = imageSize
      let drawHeight = imageSize
      let drawX = imageX
      let drawY = imageY
      
      if (imageAspectRatio > 1) {
        // 图片较宽，调整高度
        drawHeight = imageSize / imageAspectRatio
        drawY = this.y - drawHeight / 2
      } else if (imageAspectRatio < 1) {
        // 图片较高，调整宽度
        drawWidth = imageSize * imageAspectRatio
        drawX = this.x - drawWidth / 2
      } else {
        // 正方形图片，居中显示
        drawX = this.x - imageSize / 2
        drawY = this.y - imageSize / 2
      }
      
      // 绘制图片
      ctx.drawImage(this.image, drawX, drawY, drawWidth, drawHeight)
      
      // 恢复裁剪
      ctx.restore()
      ctx.save()
    } else {
      // 图片未加载时，使用原来的圆形绘制作为备用
      const gradient = ctx.createRadialGradient(
        this.x - this.radius * 0.3, this.y - this.radius * 0.3, 0,
        this.x, this.y, this.radius
      )
      gradient.addColorStop(0, this.getLighterColor(this.color))
      gradient.addColorStop(1, this.color)
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
      ctx.fill()
      
              // 绘制角色边框
        ctx.strokeStyle = this.getDarkerColor(this.color)
        ctx.lineWidth = 2
        ctx.stroke()
      
              // 绘制角色高光
        ctx.fillStyle = 'rgba(255, 255, 255, 0.4)'
        ctx.beginPath()
        ctx.arc(this.x - this.radius * 0.4, this.y - this.radius * 0.4, this.radius * 0.3, 0, Math.PI * 2)
        ctx.fill()
    }
    
    // 绘制角色等级文字（已隐藏）
    // ctx.fillStyle = '#333'
    // ctx.font = `bold ${Math.max(12, this.radius * 0.4)}px Arial`
    // ctx.textAlign = 'center'
    // ctx.textBaseline = 'middle'
    // ctx.fillText(this.level.toString(), this.x, this.y)
    
    // 特殊角色视觉效果
    if (this.isSpecial) {
      // 丐帮主特殊效果：闪烁边框
      ctx.strokeStyle = '#FFD700'
      ctx.lineWidth = 3
      ctx.setLineDash([5, 5])
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.radius + 5, 0, Math.PI * 2)
      ctx.stroke()
      ctx.setLineDash([])
      
      // 添加特殊标识
      ctx.fillStyle = '#FFD700'
      ctx.font = `bold ${Math.max(10, this.radius * 0.3)}px Arial`
      ctx.fillText('点击', this.x, this.y + this.radius + 15)
    }
    
    if (this.isUltimate) {
      // 终极角色特殊效果：金色光环
      ctx.strokeStyle = '#FFD700'
      ctx.lineWidth = 4
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.radius + 8, 0, Math.PI * 2)
      ctx.stroke()
      
      // 添加终极标识
      ctx.fillStyle = '#FFD700'
      ctx.font = `bold ${Math.max(10, this.radius * 0.3)}px Arial`
      ctx.fillText('终极', this.x, this.y + this.radius + 15)
    }
    
    // 如果正在拖拽，绘制拖拽效果
    if (this.isDragging) {
      ctx.strokeStyle = '#FFD700'
      ctx.lineWidth = 3
      ctx.setLineDash([5, 5])
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.radius + 5, 0, Math.PI * 2)
      ctx.stroke()
      ctx.setLineDash([])
    }
    

    

    
    ctx.restore()
  }
  
  /**
   * 获取更亮的颜色
   */
  getLighterColor(color) {
    // 简单的颜色变亮逻辑
    const hex = color.replace('#', '')
    const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + 40)
    const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + 40)
    const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + 40)
    return `rgb(${r}, ${g}, ${b})`
  }
  
  /**
   * 获取更暗的颜色
   */
  getDarkerColor(color) {
    // 简单的颜色变暗逻辑
    const hex = color.replace('#', '')
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 40)
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 40)
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 40)
    return `rgb(${r}, ${g}, ${b})`
  }
  
  /**
   * 绘制角色种子
   */
  drawSeeds(ctx) {
    if (this.level >= 6) { // 只有较大的角色才有种子
      ctx.fillStyle = '#2C3E50'
      const seedCount = Math.floor(this.radius / 8)
      
      for (let i = 0; i < seedCount; i++) {
        const angle = (i / seedCount) * Math.PI * 2
        const distance = this.radius * 0.6
        const seedX = this.x + Math.cos(angle) * distance
        const seedY = this.y + Math.sin(angle) * distance
        
        ctx.beginPath()
        ctx.arc(seedX, seedY, 2, 0, Math.PI * 2)
        ctx.fill()
      }
    }
  }

  /**
   * 检查是否堆叠在其他角色上
   * @param {Array} otherFruits - 其他角色数组
   */
  checkStacking(otherFruits) {
    if (this.isStatic) return
    
    const screenWidth = wx.getSystemInfoSync().screenWidth
    const screenHeight = wx.getSystemInfoSync().screenHeight
    
    for (let other of otherFruits) {
      if (other === this) continue
      
      const dx = this.x - other.x
      const dy = this.y - other.y
      const distance = Math.sqrt(dx * dx + dy * dy)
      const minDistance = this.radius + other.radius
      
      // 检查是否在其他角色上方
      if (distance < minDistance && this.y > other.y) {
        // 计算需要的分离距离
        const overlap = minDistance - distance
        const separationY = Math.min(overlap * 0.5, 5) // 限制分离距离
        
        // 计算新位置
        const newY1 = this.y + separationY
        const newY2 = other.y - separationY
        
        // 检查新位置是否在屏幕内
        if (newY1 + this.radius <= screenHeight && newY1 - this.radius >= 0) {
          this.y = newY1
        }
        
        if (newY2 + other.radius <= screenHeight && newY2 - other.radius >= 0) {
          other.y = newY2
        }
        
        // 如果速度很小，设为静态
        if (Math.abs(this.vx) < 0.5 && Math.abs(this.vy) < 0.5) {
          this.isStatic = true
          this.vx = 0
          this.vy = 0
        }
        
        // 给下面的角色一些向上的力，但限制大小
        if (Math.abs(other.vy) < 0.1) {
          other.vy = Math.max(-1, -0.2) // 大幅降低向上的力
        }
      }
    }
    
    // 额外的边界检查，确保角色不会超出屏幕
    this.enforceBoundaryConstraints(screenWidth, screenHeight)
  }
  
  /**
   * 强制边界约束
   */
  enforceBoundaryConstraints(screenWidth, screenHeight) {
    // 水平边界约束
    if (this.x - this.radius < 0) {
      this.x = this.radius
      this.vx = Math.max(0, this.vx) // 防止向左飞出
    } else if (this.x + this.radius > screenWidth) {
      this.x = screenWidth - this.radius
      this.vx = Math.min(0, this.vx) // 防止向右飞出
    }
    
    // 垂直边界约束
    if (this.y - this.radius < 0) {
      this.y = this.radius
      this.vy = Math.max(0, this.vy) // 防止向上飞出
    } else if (this.y + this.radius > screenHeight) {
      this.y = screenHeight - this.radius
      this.vy = Math.min(0, this.vy) // 防止向下飞出
    }
  }
} 