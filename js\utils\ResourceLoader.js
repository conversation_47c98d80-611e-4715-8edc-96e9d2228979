export class ResourceLoader {
  constructor() {
    this.resources = {
      images: [],
      audio: []
    }
    this.loadedImages = new Map() // 存储已加载的图片
    this.loadedAudio = new Map() // 存储已加载的音频
    this.loadedCount = 0
    this.totalCount = 0
    this.isLoading = false
    this.retryCount = 0
    this.maxRetry = 3
    this.onProgress = null
    this.onComplete = null
    this.onError = null
  }

  // 扫描资源目录，生成资源列表
  scanResources() {
    // 图片资源列表（头像图片优先加载）
    this.resources.images = [
      // 头像图片（优先加载）
      'game_media/images/char_1.png',
      'game_media/images/char_2.png',
      'game_media/images/char_3.png',
      'game_media/images/char_4.png',
      'game_media/images/char_5.png',
      'game_media/images/char_6.png',
      'game_media/images/char_7.png',
      'game_media/images/char_8.png',
      'game_media/images/char_9.png',
      'game_media/images/char_10.png',
      'game_media/images/char_11.png',
      'game_media/images/char_12.png',
      // 其他图片
      'game_media/images/11.png',
      'game_media/images/bg_1_kindergarten.png',
      'game_media/images/bg_2_tutor.png',
      'game_media/images/bg_3_classroom.png',
      'game_media/images/bg_4_university.png',
      'game_media/images/bg_5_laboratory.png',
      'game_media/images/bg_6_library.png',
      'game_media/images/bg_7_office.png',
      'game_media/images/bg_8_startup.png',
      'game_media/images/bg_9_gov.png',
      'game_media/images/bg_10_luxury.png',
      'game_media/images/bg_11_win.png'
    ]

    // 音频资源列表
    this.resources.audio = [
      'game_media/audio/click.mp3',
      'game_media/audio/drop.mp3',
      'game_media/audio/gameplay.mp3',
      'game_media/audio/lose.mp3',
      'game_media/audio/menu.mp3',
      'game_media/audio/synthesis.mp3',
      'game_media/audio/transform.mp3',
      'game_media/audio/win.mp3'
    ]

    this.totalCount = this.resources.images.length + this.resources.audio.length
    this.loadedCount = 0
  }

  // 开始加载所有资源
  async startLoading() {
    if (this.isLoading) return
    this.isLoading = true
    this.retryCount = 0
    this.loadedCount = 0
    this.scanResources()
    
    try {
      await this.loadAllResources()
    } catch (error) {
      this.handleError(error)
    }
  }

  // 加载所有资源
  async loadAllResources() {
    const promises = []
    
    // 加载图片资源
    this.resources.images.forEach((path, index) => {
      promises.push(this.loadImage(path, index))
    })
    
    // 加载音频资源
    this.resources.audio.forEach((path, index) => {
      promises.push(this.loadAudio(path, this.resources.images.length + index))
    })
    
    await Promise.all(promises)
    
    if (this.onComplete) {
      this.onComplete()
    }
  }

  // 加载单个图片
  async loadImage(path, index) {
    return new Promise((resolve, reject) => {
      const img = wx.createImage()
      img.onload = () => {
        this.loadedCount++
        this.loadedImages.set(path, img) // 缓存已加载的图片
        this.updateProgress('正在加载资源...')
        resolve(img)
      }
      img.onerror = () => {
        reject(new Error(`图片加载失败: ${path}`))
      }
      img.src = path
    })
  }

  // 加载单个音频
  async loadAudio(path, index) {
    return new Promise((resolve, reject) => {
      const audio = wx.createInnerAudioContext()
      audio.src = path
      audio.onCanplay(() => {
        this.loadedCount++
        this.loadedAudio.set(path, audio) // 缓存已加载的音频
        this.updateProgress('正在加载资源...')
        resolve(audio)
      })
      audio.onError(() => {
        reject(new Error(`音频加载失败: ${path}`))
      })
    })
  }

  // 更新进度
  updateProgress(message) {
    const progress = this.totalCount > 0 ? this.loadedCount / this.totalCount : 0
    if (this.onProgress) {
      this.onProgress(progress, message)
    }
  }

  // 处理错误
  handleError(error) {
    this.retryCount++
    if (this.retryCount <= this.maxRetry) {
      // 自动重试
      setTimeout(() => {
        this.startLoading()
      }, 1000)
    } else {
      // 达到最大重试次数
      if (this.onError) {
        this.onError(error, this.retryCount)
      }
    }
  }

  // 手动重试
  retry() {
    this.retryCount = 0
    this.startLoading()
  }

  // 设置回调函数
  setOnProgress(callback) {
    this.onProgress = callback
  }

  setOnComplete(callback) {
    this.onComplete = callback
  }

  setOnError(callback) {
    this.onError = callback
  }

  // 获取已加载的图片
  getLoadedImage(path) {
    return this.loadedImages.get(path)
  }

  // 获取已加载的音频
  getLoadedAudio(path) {
    return this.loadedAudio.get(path)
  }

  // 检查图片是否已加载
  isImageLoaded(path) {
    return this.loadedImages.has(path)
  }

  // 检查音频是否已加载
  isAudioLoaded(path) {
    return this.loadedAudio.has(path)
  }

  // 检查所有头像图片是否已加载
  areCharacterImagesLoaded() {
    const characterPaths = [
      'game_media/images/char_1.png',
      'game_media/images/char_2.png',
      'game_media/images/char_3.png',
      'game_media/images/char_4.png',
      'game_media/images/char_5.png',
      'game_media/images/char_6.png',
      'game_media/images/char_7.png',
      'game_media/images/char_8.png',
      'game_media/images/char_9.png',
      'game_media/images/char_10.png',
      'game_media/images/char_11.png',
      'game_media/images/char_12.png'
    ]
    
    return characterPaths.every(path => this.loadedImages.has(path))
  }

  // 获取头像图片加载进度
  getCharacterImagesProgress() {
    const characterPaths = [
      'game_media/images/char_1.png',
      'game_media/images/char_2.png',
      'game_media/images/char_3.png',
      'game_media/images/char_4.png',
      'game_media/images/char_5.png',
      'game_media/images/char_6.png',
      'game_media/images/char_7.png',
      'game_media/images/char_8.png',
      'game_media/images/char_9.png',
      'game_media/images/char_10.png',
      'game_media/images/char_11.png',
      'game_media/images/char_12.png'
    ]
    
    const loadedCount = characterPaths.filter(path => this.loadedImages.has(path)).length
    return {
      loaded: loadedCount,
      total: characterPaths.length,
      progress: loadedCount / characterPaths.length
    }
  }
} 