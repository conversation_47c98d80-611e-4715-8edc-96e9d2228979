/**
 * 微信官方物理引擎适配器
 * 基于微信小游戏官方物理引擎 API
 * 提供与现有 Box2D 代码兼容的接口
 */

export class WeChatPhysicsEngine {
  constructor() {
    this.world = null
    this.bodies = new Map()
    this.isLoaded = false
    this.mergeCallback = null
    this.gravity = { x: 0, y: 9.8 }
    this.timeStep = 1/60
    this.velocityIterations = 6
    this.positionIterations = 2
  }

  /**
   * 初始化微信物理引擎
   */
  async init() {
    try {
      // 检查微信物理引擎是否可用
      if (typeof wx === 'undefined' || typeof wx.createPhysicsWorld !== 'function') {
        throw new Error('微信物理引擎API不可用，当前环境不支持wx.createPhysicsWorld')
      }

      // 创建物理世界
      this.world = wx.createPhysicsWorld({
        gravity: this.gravity,
        allowSleep: true,
        autoClearForces: true
      })

      console.log('微信物理引擎初始化成功')
      this.isLoaded = true
      return this
    } catch (error) {
      console.error('微信物理引擎初始化失败:', error)
      throw error
    }
  }

  /**
   * 创建角色刚体
   * @param {Object} fruit - 角色对象
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @returns {Object} 刚体对象
   */
  createFruitBody(fruit, x, y) {
    if (!this.world) {
      console.warn('物理世界未初始化')
      return null
    }

    try {
      // 创建刚体定义
      const bodyDef = {
        type: 'dynamic', // 动态刚体
        position: { x: x / 30, y: y / 30 }, // 转换为物理单位
        linearDamping: 0.05, // 线性阻尼
        angularDamping: 0.05, // 角阻尼
        allowSleep: true,
        awake: true,
        bullet: false
      }

      // 创建圆形碰撞体
      const fixtureDef = {
        shape: {
          type: 'circle',
          radius: fruit.radius / 30 // 转换为物理单位
        },
        density: fruit.radius / 10, // 密度
        friction: 0.2, // 摩擦力
        restitution: 0.1, // 弹性
        isSensor: false
      }

      // 创建刚体
      const body = this.world.createBody(bodyDef)
      const fixture = body.createFixture(fixtureDef)

      // 存储刚体引用
      const bodyData = {
        body: body,
        fixture: fixture,
        fruit: fruit,
        isStatic: false
      }

      this.bodies.set(fruit, bodyData)

      console.log(`角色 ${fruit.name} 物理刚体创建成功`)
      return bodyData

    } catch (error) {
      console.error(`创建角色刚体失败:`, error)
      return null
    }
  }

  /**
   * 移除角色刚体
   * @param {Object} fruit - 角色对象
   */
  removeFruitBody(fruit) {
    const bodyData = this.bodies.get(fruit)
    if (bodyData && bodyData.body) {
      try {
        this.world.destroyBody(bodyData.body)
        this.bodies.delete(fruit)
        console.log(`角色 ${fruit.name} 物理刚体已移除`)
      } catch (error) {
        console.error(`移除角色刚体失败:`, error)
      }
    }
  }

  /**
   * 物理步进
   * @param {number} timeStep - 时间步长
   * @param {number} velocityIterations - 速度迭代次数
   * @param {number} positionIterations - 位置迭代次数
   */
  step(timeStep = this.timeStep, velocityIterations = this.velocityIterations, positionIterations = this.positionIterations) {
    if (!this.world || !this.isLoaded) {
      console.warn('物理世界未初始化，跳过物理步进')
      return
    }

    try {
      // 执行物理步进
      this.world.step(timeStep, velocityIterations, positionIterations)

      // 同步位置到角色对象
      this.bodies.forEach((bodyData, fruit) => {
        if (bodyData.body && !bodyData.isStatic) {
          const position = bodyData.body.getPosition()
          const velocity = bodyData.body.getLinearVelocity()

          // 转换回游戏单位
          fruit.x = position.x * 30
          fruit.y = position.y * 30
          fruit.vx = velocity.x
          fruit.vy = velocity.y

          // 检查是否静止
          const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y)
          if (speed < 0.1) {
            fruit.isStatic = true
            fruit.vx = 0
            fruit.vy = 0
          }
        }
      })

    } catch (error) {
      console.error('物理步进失败:', error)
    }
  }

  /**
   * 设置角色位置
   * @param {Object} fruit - 角色对象
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setFruitPosition(fruit, x, y) {
    const bodyData = this.bodies.get(fruit)
    if (bodyData && bodyData.body) {
      try {
        bodyData.body.setPosition({ x: x / 30, y: y / 30 })
      } catch (error) {
        console.error('设置角色位置失败:', error)
      }
    }
  }

  /**
   * 对角色施加力
   * @param {Object} fruit - 角色对象
   * @param {number} forceX - X方向力
   * @param {number} forceY - Y方向力
   */
  applyForceToFruit(fruit, forceX, forceY) {
    const bodyData = this.bodies.get(fruit)
    if (bodyData && bodyData.body) {
      try {
        bodyData.body.applyForce({ x: forceX, y: forceY })
      } catch (error) {
        console.error('施加力失败:', error)
      }
    }
  }

  /**
   * 设置合成回调
   * @param {Function} callback - 合成回调函数
   */
  setMergeCallback(callback) {
    this.mergeCallback = callback
  }

  /**
   * 设置重力
   * @param {number} x - X方向重力
   * @param {number} y - Y方向重力
   */
  setGravity(x, y) {
    if (this.world) {
      try {
        this.world.setGravity({ x: x, y: y })
        this.gravity = { x: x, y: y }
      } catch (error) {
        console.error('设置重力失败:', error)
      }
    }
  }

  /**
   * 获取重力
   * @returns {Object} 重力向量
   */
  getGravity() {
    return this.gravity
  }

  /**
   * 销毁物理引擎
   */
  destroy() {
    try {
      if (this.world) {
        // 清理所有刚体
        this.bodies.forEach((bodyData, fruit) => {
          this.removeFruitBody(fruit)
        })

        // 销毁物理世界
        this.world.destroy()
        this.world = null
        this.bodies.clear()
        this.isLoaded = false

        console.log('微信物理引擎已销毁')
      }
    } catch (error) {
      console.error('销毁物理引擎失败:', error)
    }
  }

  /**
   * 获取物理世界状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      isLoaded: this.isLoaded,
      bodyCount: this.bodies.size,
      gravity: this.gravity,
      timeStep: this.timeStep
    }
  }
}

import { initSimplePhysics } from './simple-physics-fallback.js'

/**
 * 微信物理引擎初始化函数
 * 提供与现有 initBox2D 兼容的接口
 */
export async function initWeChatPhysics() {
  try {
    const physicsEngine = new WeChatPhysicsEngine()
    await physicsEngine.init()
    
    // 返回兼容的接口
    return {
      useWeChatPhysics: true,
      isLoaded: true,
      world: physicsEngine.world,
      bodies: physicsEngine.bodies,
      createFruitBody: physicsEngine.createFruitBody.bind(physicsEngine),
      removeFruitBody: physicsEngine.removeFruitBody.bind(physicsEngine),
      step: physicsEngine.step.bind(physicsEngine),
      setFruitPosition: physicsEngine.setFruitPosition.bind(physicsEngine),
      applyForceToFruit: physicsEngine.applyForceToFruit.bind(physicsEngine),
      setMergeCallback: physicsEngine.setMergeCallback.bind(physicsEngine),
      setGravity: physicsEngine.setGravity.bind(physicsEngine),
      getGravity: physicsEngine.getGravity.bind(physicsEngine),
      destroy: physicsEngine.destroy.bind(physicsEngine),
      getStatus: physicsEngine.getStatus.bind(physicsEngine),
      // 保存引擎实例以便后续使用
      engine: physicsEngine
    }
  } catch (error) {
    console.error('微信物理引擎初始化失败，使用简化物理引擎:', error)
    // 返回简化物理引擎作为备用方案
    return initSimplePhysics()
  }
}

// 导出兼容的初始化函数
export const initBox2D = initWeChatPhysics 