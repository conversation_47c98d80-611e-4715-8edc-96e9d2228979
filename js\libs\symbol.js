// Symbol polyfill for WeChat Mini Game
if (typeof Symbol === 'undefined') {
  global.Symbol = function(name) {
    return name
  }
  
  Symbol.iterator = '@@iterator'
  Symbol.asyncIterator = '@@asyncIterator'
  Symbol.hasInstance = '@@hasInstance'
  Symbol.isConcatSpreadable = '@@isConcatSpreadable'
  Symbol.match = '@@match'
  Symbol.replace = '@@replace'
  Symbol.search = '@@search'
  Symbol.species = '@@species'
  Symbol.split = '@@split'
  Symbol.toPrimitive = '@@toPrimitive'
  Symbol.toStringTag = '@@toStringTag'
  Symbol.unscopables = '@@unscopables'
} 