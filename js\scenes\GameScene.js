import { Fruit } from '../objects/Fruit.js'
import { ParticleSystem } from '../utils/ParticleSystem.js'
import { physicsManager } from '../libs/physics-manager.js'
import { LoadingScreen } from '../utils/LoadingScreen.js'
import { objectPool } from '../utils/ObjectPool.js'
import { Y2KUIConfig } from '../config/Y2KUIConfig.js'
import { PixelEffects } from '../utils/PixelEffects.js'
import { CyberDecorations } from '../utils/CyberDecorations.js'

export class GameScene {
  constructor(main) {
    this.main = main
    this.screenWidth = main.screenWidth
    this.screenHeight = main.screenHeight
    this.gameData = main.gameData

    // Y2K UI系统
    this.pixelEffects = new PixelEffects(main.canvas, main.ctx)
    this.cyberDecorations = new CyberDecorations(main.canvas, main.ctx)

    // 游戏状态
    this.isStarted = false
    this.draggedFruit = null
    this.nextFruit = null

    // 警戒线配置（移动到出球点下方）
    this.updateWarningLinePosition()

    // 警戒线接触状态管理
    this.warningLineContact = {
      isContacting: false,    // 是否有角色接触警戒线
      contactStartTime: 0,    // 开始接触的时间
      contactDuration: 1500,  // 持续接触1.5秒后结束（毫秒）
      lastCheckTime: 0        // 上次检查时间
    }

    // 粒子系统
    this.particleSystem = new ParticleSystem()

    // 物理引擎
    this.physicsEngine = null

    // 加载提示
    this.loadingScreen = new LoadingScreen(main)

    // 初始化第一个角色
    this.gameData.generateNextFruit()
    this.nextFruit = this.gameData.nextFruit

    // 帧计数器（用于优化悬空检测频率）
    this.frameCount = 0

    // 发射控制
    this.lastShootTime = 0
    this.shootCooldown = 300 // 发射冷却时间（毫秒）
    this.isShooting = false // 是否正在发射

    // 合成控制
    this.mergeCooldown = 50 // 合成冷却时间（毫秒）
    this.lastMergeTime = 0
    this.isMerging = false // 是否正在合成

    // 性能监控
    this.performanceStats = {
      frameCount: 0,
      averageFrameTime: 0,
      lastFrameTime: 0
    }

    // 内存管理：定时器ID存储
    this.pendingTimeouts = new Set()

    // 预览图片缓存（使用ResourceLoader中的图片）
    this.previewImageCache = new Map()

    // 背景图片管理
    this.backgroundImages = new Map()
    this.currentBackgroundLevel = 1
    this.currentBackgroundImage = null
    this.backgroundImagesInitialized = false

    // Y2K风格菜单按钮
    this.menuButton = {
      x: 70, // Will be set in start()
      y: 140, // Will be set in start()
      width: Y2KUIConfig.DIMENSIONS.BUTTON_ICON.width,
      height: Y2KUIConfig.DIMENSIONS.BUTTON_ICON.height,
      color: Y2KUIConfig.COLORS.ELECTRIC_PURPLE,
      hoverColor: Y2KUIConfig.COLORS.NEON_PINK,
      borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
      isHovered: false,
      pulseAnimation: 1,
      glitchEffect: false
    }
    
    // 卡通游戏风格计分窗口
    this.scoreDisplay = {
      x: 70, // Will be set in start()
      y: 210, // Will be set in start()
      width: 160, // 卡通风格宽度
      height: 70,  // 增加高度以容纳两行文字
      currentScore: 0,
      highScore: 0,
      level: 1,
      glitchEffect: false,
      animationTimer: 0
    }

    // Y2K风格游戏道具
    this.powerUpItem = {
      x: 0, // Will be set in start()
      y: 70, // Will be set in start()
      width: Y2KUIConfig.DIMENSIONS.BUTTON_ICON.width + 20, // 放大按钮
      height: Y2KUIConfig.DIMENSIONS.BUTTON_ICON.height + 20, // 放大按钮
      color: Y2KUIConfig.COLORS.NEON_PINK,
      hoverColor: Y2KUIConfig.COLORS.LIQUID_ORANGE,
      borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
      isHovered: false,
      isAvailable: true,
      count: 3,
      pulseAnimation: 1,
      glitchEffect: false,
      imagePath: 'game_media/images/thanos.png',
      image: null,
      imageLoaded: false
    }
    
    // 菜单弹窗
    this.menuModal = {
      isOpen: false,
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      buttons: []
    }
    
    // 游戏暂停状态
    this.isPaused = false
    
    // 绑定事件
    // 不再直接绑定事件，由事件管理器统一处理
  }
  
  async start() {
    // 初始化物理引擎
    try {
      this.physicsEngine = await physicsManager.init()
      const status = physicsManager.getStatus()
      // console.log(`物理引擎初始化成功，类型: ${status.engineType}`)
      // console.log('物理引擎状态详情:', status)
      
      // 创建墙壁
      const walls = physicsManager.createWalls(this.screenWidth, this.screenHeight, 50)
      // console.log('墙壁创建成功，数量:', walls.length)
      
      // 预创建对象池
      const levels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] // 所有等级
      await objectPool.preCreatePools(levels, this.gameData)
      
    } catch (error) {
      console.warn('物理引擎初始化失败:', error)
      this.physicsEngine = null
    }
    
    // 只在第一次启动时初始化背景图片
    if (!this.backgroundImagesInitialized) {
      await this.initBackgroundImages()
      this.backgroundImagesInitialized = true
    }
    
    this.gameData.reset()
    this.resetWarningLineContact()
    this.isStarted = true
    this.generateNextCharacter()
    
    // 设置初始背景（基于当前最高等级）
    const initialHighestLevel = this.getHighestLevelInGame()
    const initialBackgroundLevel = this.getBackgroundLevel(initialHighestLevel)
    this.currentBackgroundLevel = initialBackgroundLevel
    const backgroundData = this.backgroundImages.get(initialBackgroundLevel)
    if (backgroundData && backgroundData.image) {
      this.currentBackgroundImage = backgroundData.image
    }
    
    // 计算UI元素位置
    this.calculateUIPositions()
    
    // 初始化道具图片
    if (this.powerUpItem.imagePath && !this.powerUpItem.imageLoaded) {
      try {
        this.powerUpItem.image = wx.createImage()
        this.powerUpItem.image.onload = () => {
          this.powerUpItem.imageLoaded = true
        }
        this.powerUpItem.image.onerror = () => {
          console.warn(`道具图片加载失败: ${this.powerUpItem.imagePath}`)
          this.powerUpItem.imageLoaded = false
        }
        this.powerUpItem.image.src = this.powerUpItem.imagePath
      } catch (error) {
        console.warn(`创建道具图片对象失败: ${this.powerUpItem.imagePath}`, error)
        this.powerUpItem.imageLoaded = false
      }
    }
    
    // 初始化计时器（已移除）
    // this.timer.startTime = Date.now()
    // this.timer.currentTime = 0
    
    // 初始化计分器数据
    this.scoreDisplay.currentScore = this.gameData.score
    this.scoreDisplay.highScore = this.gameData.highScore
    this.scoreDisplay.level = this.gameData.currentLevel
  }

  /**
   * 重置警戒线接触状态
   */
  resetWarningLineContact() {
    this.warningLineContact.isContacting = false
    this.warningLineContact.contactStartTime = 0
    this.warningLineContact.lastCheckTime = 0
  }
  

  
  /**
   * 处理触摸开始事件（由事件管理器调用）
   */
  async handleTouchStart(x, y) {
    // 检查菜单弹窗是否打开
    if (this.menuModal.isOpen) {
      // 检查是否点击了菜单按钮
      const clickedButton = this.checkMenuButtonClick(x, y)
      if (clickedButton) {
        this.handleMenuButtonClick(clickedButton)
        return
      }
      
      // 点击弹窗外部关闭弹窗
      if (!this.isPointInMenuModal(x, y)) {
        this.closeMenuModal()
        return
      }
      
      return
    }
    
    // 检查菜单按钮点击
    if (x >= this.menuButton.x - this.menuButton.width / 2 && 
        x <= this.menuButton.x + this.menuButton.width / 2 &&
        y >= this.menuButton.y - this.menuButton.height / 2 && 
        y <= this.menuButton.y + this.menuButton.height / 2) {
      this.openMenuModal()
      return
    }
    
    // 检查道具按钮点击
    if (x >= this.powerUpItem.x - this.powerUpItem.width / 2 && 
        x <= this.powerUpItem.x + this.powerUpItem.width / 2 &&
        y >= this.powerUpItem.y - this.powerUpItem.height / 2 && 
        y <= this.powerUpItem.y + this.powerUpItem.height / 2) {
      if (this.powerUpItem.isAvailable && this.powerUpItem.count > 0) {
        this.usePowerUp()
      }
      return
    }
    
    // 检查音频控制按钮点击（已移除）
    // if (x >= this.audioButton.x - this.audioButton.width / 2 && 
    //     x <= this.audioButton.x + this.audioButton.width / 2 &&
    //     y >= this.audioButton.y - this.audioButton.height / 2 && 
    //     y <= this.audioButton.y + this.audioButton.height / 2) {
    //   this.main.audioManager.toggleMute()
    //   
    //   // 显示状态提示
    //   const isMuted = this.main.audioManager.isMuted
    //   wx.showToast({
    //     title: isMuted ? '音频已关闭' : '音频已开启',
    //     icon: 'none',
    //     duration: 1000
    //   })
    //   
    //   return
    // }
    
    if (!this.isStarted) {
      console.log('❌ 游戏未开始，忽略点击')
      return
    }
    
    // 检查发射冷却时间
    const currentTime = Date.now()
    if (currentTime - this.lastShootTime < this.shootCooldown) {
              // console.log('⏰ 发射冷却中，忽略点击')
      return
    }
    
    // 检查是否正在发射
    if (this.isShooting) {
              // console.log('🚫 正在发射中，忽略点击')
      return
    }
    
    // 首先检查是否点击了特殊角色（丐帮主）
    for (let fruit of this.gameData.fruits) {
      if (fruit.checkSpecialClick(x, y)) {
        console.log('🎯 点击了特殊角色')
        // 处理特殊角色点击
        const bonusScore = fruit.handleSpecialClick()
        if (bonusScore > 0) {
          this.gameData.addScore(bonusScore)
          this.gameData.removeFruit(fruit)
          
          // 显示奖励提示
          wx.showToast({
            title: `+${bonusScore}分！`,
            icon: 'success',
            duration: 1500
          })
          
          // 播放转换音效
          try {
            this.main.audioManager.playTransformSound()
          } catch (error) {
            console.warn('Failed to play transform sound:', error)
          }
          
          return // 点击了特殊角色，不执行掉落逻辑
        }
      }
    }
    
    // 移除掉落区域限制，允许点击屏幕任意位置
    if (this.nextFruit) {
      // 设置发射状态
      this.isShooting = true
      this.lastShootTime = currentTime
      
      // 角色从点击位置的正上方掉落
      const fruitX = Math.max(this.nextFruit.radius, Math.min(x, this.screenWidth - this.nextFruit.radius))
      const fruitY = 50 + this.nextFruit.radius // 从屏幕顶部附近掉落
      
      // 使用对象池获取球体
      const fruit = await objectPool.getFruit(this.nextFruit.level, fruitX, fruitY, this.gameData)
      
      // 直接掉落，不需要拖拽
      this.gameData.addFruit(fruit)
      this.generateNextCharacter()
      
      // 播放掉落音效
      try {
        this.main.audioManager.playDropSound()
      } catch (error) {
        console.warn('Failed to play drop sound:', error)
      }
      
      // 重置发射状态
      this.isShooting = false
    } else {
      console.log('❌ nextFruit 为空，无法创建角色')
    }
  }
  
  /**
   * 处理触摸移动事件（由事件管理器调用）
   */
  handleTouchMove(x, y) {
    if (!this.isStarted || !this.draggedFruit) return
    
    // 移动时更新下一个角色的预览位置
    if (this.nextFruit) {
      // 可以在这里添加预览效果
    }
  }
  
  /**
   * 处理触摸结束事件（由事件管理器调用）
   */
  handleTouchEnd() {
    if (!this.isStarted || !this.draggedFruit) return
    
    // 触摸结束时的处理
  }
  
  /**
   * 清理事件监听（场景切换时调用）
   */
  cleanupEvents() {
    // 游戏场景没有需要清理的事件监听，由事件管理器统一管理
    
    // 清理所有待处理的定时器
    this.clearAllTimeouts()
  }
  
  /**
   * 清理游戏状态（返回首页时调用）
   */
  cleanupGameState() {
    // 重置游戏状态
    this.isStarted = false
    this.isPaused = false
    this.draggedFruit = null
    this.nextFruit = null
    
    // 清理所有球体
    this.gameData.fruits = []
    
    // 重置游戏数据
    this.gameData.reset()
    
    // 重置警戒线接触状态
    this.resetWarningLineContact()
    
    // 清理所有待处理的定时器
    this.clearAllTimeouts()
    
    // 重置UI元素状态
    this.menuModal.isOpen = false
    this.menuModal.buttons = []
    
    // 重置道具状态
    this.powerUpItem.count = 3
    this.powerUpItem.isAvailable = true
    
    // 重置计时器（已移除）
    // this.timer.currentTime = 0
    // this.timer.startTime = 0
    
    // 重置计分器
    this.scoreDisplay.currentScore = 0
    this.scoreDisplay.level = 1
    
    // 重置背景（保持图片缓存）
    this.currentBackgroundLevel = 1
    const backgroundData = this.backgroundImages.get(1)
    if (backgroundData && backgroundData.image) {
      this.currentBackgroundImage = backgroundData.image
    }
    // 注意：不重置 this.backgroundImagesInitialized，保持图片缓存
    
    // 清理粒子系统
    this.particleSystem.clear()
    
    // 重置物理引擎（如果存在）
    if (physicsManager.isReady()) {
      try {
        physicsManager.reset()
      } catch (error) {
        console.warn('重置物理引擎失败:', error)
      }
    }
  }
  
  generateNextCharacter() {
    this.gameData.generateNextFruit()
    this.nextFruit = this.gameData.nextFruit
    // console.log(`🎲 生成下一个角色: ${this.nextFruit ? this.nextFruit.name : 'null'}`)
  }
  
  update() {
    if (!this.isStarted) return
    
    const frameStartTime = performance.now()
    this.frameCount++
    this.performanceStats.frameCount++
    
    // === 使用新的固定时间步长物理引擎 ===
    if (physicsManager.isReady()) {
      // 计算精确的deltaTime
      const currentTime = performance.now()
      const deltaTime = this.lastUpdateTime ? currentTime - this.lastUpdateTime : 16.67
      this.lastUpdateTime = currentTime
      
      // 更宽松的deltaTime限制，适应实际帧率
      const clampedDeltaTime = Math.min(Math.max(deltaTime, 8), 50) // 限制在8-50ms范围内
      
      // 只在deltaTime严重异常时记录警告
      if (deltaTime > 50) {
        // console.warn(`⚠️ DeltaTime严重异常: ${deltaTime.toFixed(2)}ms，已限制为 ${clampedDeltaTime}ms`)
      }
      
      // 自适应性能调整
      if (deltaTime > 30) {
        // 如果帧时间过长，减少物理更新频率
        if (this.frameCount % 2 === 0) {
          return // 跳过这一帧的物理更新
        }
      }
      
      // 更新物理引擎（使用固定时间步长）
      physicsManager.update(clampedDeltaTime)
      
      // === 4. 直接状态读取 - 渲染时直接读取物理引擎的实时状态 ===
      this.gameData.fruits.forEach(fruit => {
        if (fruit.body) {
          // 直接使用body的当前位置和角度
          fruit.x = fruit.body.position.x
          fruit.y = fruit.body.position.y
          fruit.vx = fruit.body.velocity.x
          fruit.vy = fruit.body.velocity.y
          fruit.angle = fruit.body.angle
          fruit.angularVelocity = fruit.body.angularVelocity
        } else {
          // 回退到传统更新方式
          fruit.update()
        }
      })
    } else {
      // 如果没有物理引擎，使用简单物理更新
      this.gameData.fruits.forEach(fruit => {
        fruit.update()
      })
    }
    
    // 检查堆叠
    this.gameData.fruits.forEach(fruit => {
      fruit.checkStacking(this.gameData.fruits)
    })
    
    // 提高碰撞检测频率，模拟CCD，确保无重叠
    const collisionIterations = 4 // 可根据需要调整
    for (let i = 0; i < collisionIterations; i++) {
      this.checkCollisions()
    }
    
    // 全局边界约束检查
    this.enforceGlobalBoundaryConstraints()
    
    // 检查游戏结束条件
    this.checkGameOver()
    
    // 检查升级
    if (this.gameData.checkLevelUp()) {
      this.showLevelUpEffect()
    }
    
    // 更新粒子系统
    this.particleSystem.update()

    // 更新Y2K效果系统
    this.pixelEffects.update(16.67)
    this.cyberDecorations.update(16.67)

    // 更新UI数据
    if (!this.isPaused) {
      this.scoreDisplay.currentScore = this.gameData.score
      this.scoreDisplay.highScore = this.gameData.highScore
      this.scoreDisplay.level = this.gameData.currentLevel

      // 移除分数故障效果
    }

    // 更新按钮动画
    this.updateY2KButtonAnimations()
  }
  
  /**
   * 全局边界约束检查
   */
  enforceGlobalBoundaryConstraints() {
    const fruits = this.gameData.fruits
    const screenWidth = this.screenWidth
    const screenHeight = this.screenHeight
    
    fruits.forEach(fruit => {
      // 确保角色不会超出屏幕边界
      if (fruit.x - fruit.radius < 0) {
        fruit.x = fruit.radius
        fruit.vx = Math.max(0, fruit.vx)
      } else if (fruit.x + fruit.radius > screenWidth) {
        fruit.x = screenWidth - fruit.radius
        fruit.vx = Math.min(0, fruit.vx)
      }
      
      if (fruit.y - fruit.radius < 0) {
        fruit.y = fruit.radius
        fruit.vy = Math.max(0, fruit.vy)
      } else if (fruit.y + fruit.radius > screenHeight) {
        fruit.y = screenHeight - fruit.radius
        fruit.vy = Math.min(0, fruit.vy)
      }
    })
  }
  
  async checkCollisions() {
    const fruits = this.gameData.fruits
    
    for (let i = 0; i < fruits.length; i++) {
      const fruit1 = fruits[i]
      
      // 跳过已标记为待合成的球体
      if (fruit1.isMarkedForMerge) {
        continue
      }
      
      for (let j = i + 1; j < fruits.length; j++) {
        const fruit2 = fruits[j]
        
        // 跳过已标记为待合成的球体
        if (fruit2.isMarkedForMerge) {
          continue
        }
        
        // 防止自我碰撞（检查对象ID）
        if (fruit1.objectId === fruit2.objectId) {
          console.warn(`检测到自我碰撞: 对象ID ${fruit1.objectId}`)
          continue
        }
        
        if (fruit1.checkCollision(fruit2)) {
          // 特殊角色不能被合成
          if (fruit1.isSpecial || fruit2.isSpecial) {
            // 特殊角色只进行普通碰撞处理
            fruit1.handleCollision(fruit2)
          } else if (fruit1.level === fruit2.level && fruit1.level < 12) {
            // 相同等级的角色可以合成
            // console.log(`🎯 检测到同等级碰撞: 等级 ${fruit1.level}, 位置(${fruit1.x.toFixed(1)}, ${fruit1.y.toFixed(1)}) 和 (${fruit2.x.toFixed(1)}, ${fruit2.y.toFixed(1)})`)
            await this.mergeFruits(fruit1, fruit2)
          } else {
            // 不同等级的角色进行普通碰撞处理
            fruit1.handleCollision(fruit2)
          }
        }
      }
    }
  }
  
  async mergeFruits(fruit1, fruit2) {
    // 防止重复合成
    if (fruit1.isMarkedForMerge || fruit2.isMarkedForMerge) return
    
    // 检查合成冷却时间
    const currentTime = Date.now()
    if (currentTime - this.lastMergeTime < this.mergeCooldown) {
      // console.log('⏰ 合成冷却中，忽略合成')
      return
    }
    
    // 检查是否正在合成
    if (this.isMerging) {
      // console.log('🚫 正在合成中，忽略合成')
      return
    }
    
    // 设置合成状态
    this.isMerging = true
    this.lastMergeTime = currentTime
    
    // 标记角色为待合成状态
    fruit1.isMarkedForMerge = true
    fruit2.isMarkedForMerge = true
    
    // console.log(`✨ 开始合成: 等级 ${fruit1.level} -> ${fruit1.level + 1}`)
    
    // 立即从游戏数据中移除
    this.gameData.removeFruit(fruit1)
    this.gameData.removeFruit(fruit2)
    
    // 计算新角色的位置（取两个角色的中心点）
    const newX = (fruit1.x + fruit2.x) / 2
    const newY = (fruit1.y + fruit2.y) / 2
    
    // 使用对象池创建更高级的角色
    const newLevel = fruit1.level + 1
    const newFruit = await objectPool.getFruit(newLevel, newX, newY, this.gameData)
    
    // 检查新球体是否创建成功
    if (!newFruit) {
      console.error('合成失败：无法创建新球体')
      this.isMerging = false
      return
    }
    
    // 检查并更新背景
    this.updateBackground(newLevel)
    
    // 添加合成时的缩放动画效果
    newFruit.mergeScale = 1.5 // 初始放大1.5倍
    newFruit.mergeScaleTarget = 1.0 // 目标大小
    newFruit.mergeScaleSpeed = 0.1 // 缩放速度
    
    // 设置新角色的初始速度（Q弹的合成效果）
    const centerX = (fruit1.x + fruit2.x) / 2
    const centerY = (fruit1.y + fruit2.y) / 2
    
    // 计算合成时的弹性效果
    const distance = Math.sqrt(Math.pow(fruit1.x - fruit2.x, 2) + Math.pow(fruit1.y - fruit2.y, 2))
    const elasticForce = Math.min(distance * 0.3, 8) // 弹性力，最大8像素
    
    // 计算弹跳方向（从合成中心向外）
    const angle1 = Math.atan2(fruit1.y - centerY, fruit1.x - centerX)
    const angle2 = Math.atan2(fruit2.y - centerY, fruit2.x - centerX)
    
    // 设置新球体的初始速度（Q弹效果）
    newFruit.vx = (fruit1.vx + fruit2.vx) * 0.3 + Math.cos(angle1) * elasticForce * 0.5
    newFruit.vy = Math.min(fruit1.vy, fruit2.vy) - elasticForce * 1.2 // 更强的向上弹跳
    
    // 更新物理体速度
    if (newFruit.body && physicsManager.isReady()) {
      physicsManager.getWorld().bodies.forEach(body => {
        if (body.id === newFruit.body.id) {
          body.velocity.x = newFruit.vx
          body.velocity.y = newFruit.vy
        }
      })
    }
    
    // 添加得分
    this.gameData.addScore(newFruit.score)
    
    // 添加新角色到游戏数据
    this.gameData.addFruit(newFruit)
    
    // 播放合成音效
    this.main.audioManager.playSynthesisSound()
    
    // 创建Q弹的合成特效
    this.particleSystem.createMergeEffect(newX, newY, newFruit.score)
    
    // 添加Q弹的合成光晕效果
    this.createMergeGlow(newX, newY, newFruit.radius)
    
    // 添加弹性粒子效果
    this.createElasticParticles(newX, newY, newFruit.radius)
    
    // 重置合成状态
    this.isMerging = false
    
    // console.log(`✅ 合成完成: 等级 ${newLevel}`)
    
    // 检查连锁反应（延迟执行，避免立即触发）
    const timeoutId = setTimeout(() => {
      this.pendingTimeouts.delete(timeoutId)
      this.checkChainReaction(newFruit)
    }, 150) // 连锁反应延迟，让玩家看清合成结果
    this.pendingTimeouts.add(timeoutId)
  }
  
  /**
   * 创建合成光晕效果
   */
  createMergeGlow(x, y, radius) {
    // 创建光晕粒子
    for (let i = 0; i < 20; i++) {
      const angle = (i / 20) * Math.PI * 2
      const distance = radius * 1.5
      const particleX = x + Math.cos(angle) * distance
      const particleY = y + Math.sin(angle) * distance
      
      this.particleSystem.createParticle(
        particleX,
        particleY,
        Math.cos(angle) * 2,
        Math.sin(angle) * 2,
        '#FFD700', // 金色
        20,
        1.0
      )
    }
  }
  
  /**
   * 创建弹性粒子效果
   */
  createElasticParticles(x, y, radius) {
    // 创建弹性粒子，模拟果冻的Q弹效果
    for (let i = 0; i < 15; i++) {
      const angle = (i / 15) * Math.PI * 2
      const distance = radius * (0.8 + Math.random() * 0.4) // 随机距离
      const particleX = x + Math.cos(angle) * distance
      const particleY = y + Math.sin(angle) * distance
      
      // 随机速度，模拟弹性效果
      const speed = 1 + Math.random() * 3
      const vx = Math.cos(angle) * speed
      const vy = Math.sin(angle) * speed
      
      // 使用果冻般的颜色
      const colors = ['#FF6B9D', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
      const color = colors[Math.floor(Math.random() * colors.length)]
      
      this.particleSystem.createParticle(
        particleX,
        particleY,
        vx,
        vy,
        color,
        15 + Math.random() * 10, // 随机大小
        0.8 + Math.random() * 0.4 // 随机透明度
      )
    }
  }
  
  checkChainReaction(newFruit) {
    // 检查新合成的角色是否与其他同等级角色接触
    const fruits = this.gameData.fruits
    
    for (let fruit of fruits) {
      // 跳过自己、已标记的球体和特殊角色
      if (fruit === newFruit || fruit.isMarkedForMerge || fruit.isSpecial || newFruit.isSpecial) {
        continue
      }
      
      // 检查是否同等级
      if (fruit.level === newFruit.level) {
        const distance = Math.sqrt(
          Math.pow(newFruit.x - fruit.x, 2) + 
          Math.pow(newFruit.y - fruit.y, 2)
        )
        const minDistance = newFruit.radius + fruit.radius
        
        if (distance < minDistance) {
          // 触发连锁合成（减少延迟时间）
          const timeoutId = setTimeout(() => {
            this.pendingTimeouts.delete(timeoutId)
            this.mergeFruits(newFruit, fruit)
          }, 100) // 延迟100ms避免同时触发多个合成
          this.pendingTimeouts.add(timeoutId)
          break
        }
      }
    }
  }
  
  checkGameOver() {
    const currentTime = Date.now()
    const fruits = this.gameData.fruits
    let hasContact = false
    
    // 警戒线碰撞盒参数（与视觉绘制完全匹配）
    const warningLineY = this.warningLineY
    const warningLineThickness = this.warningLineContact.isContacting ? 5 : 3 // 与渲染的lineWidth一致
    const warningLineHalfThickness = warningLineThickness / 2
    
    // 检查是否有角色接触警戒线
    for (let fruit of fruits) {
      // 精准碰撞检测：角色碰撞盒与警戒线碰撞盒的完全接触
      const fruitTop = fruit.y - fruit.radius
      const fruitBottom = fruit.y + fruit.radius
      const warningLineTop = warningLineY - warningLineHalfThickness
      const warningLineBottom = warningLineY + warningLineHalfThickness
      
      // 检查角色是否与警戒线碰撞盒完全接触
      // 角色底部必须完全接触警戒线顶部，或者角色顶部完全接触警戒线底部
      const isContacting = (
        (fruitBottom >= warningLineTop && fruitBottom <= warningLineBottom) || // 角色底部在警戒线范围内
        (fruitTop <= warningLineBottom && fruitTop >= warningLineTop) || // 角色顶部在警戒线范围内
        (fruitTop <= warningLineTop && fruitBottom >= warningLineBottom) // 角色完全穿过警戒线
      )
      
      if (isContacting) {
        hasContact = true
        break
      }
    }
    
    // 处理接触状态变化
    if (hasContact && !this.warningLineContact.isContacting) {
      // 开始接触
      this.warningLineContact.isContacting = true
      this.warningLineContact.contactStartTime = currentTime
      // console.log('警戒线开始被接触 - 精准碰撞检测')
    } else if (!hasContact && this.warningLineContact.isContacting) {
      // 停止接触，立即重置状态
      this.warningLineContact.isContacting = false
      this.warningLineContact.contactStartTime = 0
      // console.log('警戒线接触结束，立即重置计时')
    }
    
    // 检查是否持续接触超过1.5秒
    if (this.warningLineContact.isContacting) {
      const contactTime = currentTime - this.warningLineContact.contactStartTime
      const remainingTime = this.warningLineContact.contactDuration - contactTime
      
      // 每秒更新一次日志（避免日志过多）
      if (currentTime - this.warningLineContact.lastCheckTime > 1000) {
        // console.log(`警戒线持续完全接触: ${(contactTime / 1000).toFixed(1)}秒，剩余: ${(remainingTime / 1000).toFixed(1)}秒`)
        this.warningLineContact.lastCheckTime = currentTime
      }
      
      // 如果持续完全接触超过1.5秒，游戏结束
      if (contactTime >= this.warningLineContact.contactDuration) {
        // console.log('警戒线持续完全接触1.5秒，游戏结束')
        this.gameOver()
        return
      }
    }
  }
  
  gameOver() {
    this.isStarted = false
    this.gameData.gameOver = true
    
    // 更新游戏统计
    this.gameData.onGameOver()
    
    // 清理所有待处理的定时器
    this.clearAllTimeouts()
    
    // 重置警戒线接触状态
    this.resetWarningLineContact()
    
    this.main.switchScene('gameOver')
  }
  
  /**
   * 清理所有待处理的定时器
   */
  clearAllTimeouts() {
    for (const timeoutId of this.pendingTimeouts) {
      clearTimeout(timeoutId)
    }
    this.pendingTimeouts.clear()
  }
  
  /**
   * 使用游戏道具（霸总的爱）
   */
  usePowerUp() {
    if (this.powerUpItem.count > 0) {
      this.powerUpItem.count--
      
      // 道具效果：消除画面中一半的球体
      if (this.gameData.fruits.length > 0) {
        const fruitsToRemove = Math.floor(this.gameData.fruits.length / 2)
        let totalBonusScore = 0
        
        // 随机选择要移除的球体
        const fruitsCopy = [...this.gameData.fruits]
        const removedFruits = []
        
        for (let i = 0; i < fruitsToRemove; i++) {
          if (fruitsCopy.length > 0) {
            const randomIndex = Math.floor(Math.random() * fruitsCopy.length)
            const fruitToRemove = fruitsCopy.splice(randomIndex, 1)[0]
            removedFruits.push(fruitToRemove)
            
            // 计算分数奖励
            const bonusScore = Math.floor(fruitToRemove.score * 0.5)
            totalBonusScore += bonusScore
            
            // 创建特效
            this.createPowerUpEffect(fruitToRemove.x, fruitToRemove.y)
          }
        }
        
        // 批量移除球体
        removedFruits.forEach(fruit => {
          this.gameData.removeFruit(fruit)
        })
        
        // 添加总分数奖励
        this.gameData.addScore(totalBonusScore)
        
        // 显示道具使用效果
        wx.showToast({
          title: `消除${fruitsToRemove}个球体，+${totalBonusScore}分！`,
          icon: 'success',
          duration: 2000
        })
      }
      
      // 如果道具用完，设置为不可用
      if (this.powerUpItem.count <= 0) {
        this.powerUpItem.isAvailable = false
      }
    }
  }
  
  /**
   * 创建道具使用特效
   */
  createPowerUpEffect(x, y) {
    // 创建粒子特效
    for (let i = 0; i < 20; i++) {
      const angle = (Math.PI * 2 * i) / 20
      const speed = 2 + Math.random() * 3
      const vx = Math.cos(angle) * speed
      const vy = Math.sin(angle) * speed
      
      this.particleSystem.createParticle(
        x,
        y,
        vx,
        vy,
        '#E74C3C',
        3 + Math.random() * 3,
        60
      )
    }
  }
  
  /**
   * 更新按钮动画
   */
  /**
   * 更新Y2K风格按钮动画
   */
  updateY2KButtonAnimations() {
    const time = Date.now()

    // 更新菜单按钮动画
    this.menuButton.pulseAnimation = 1 + Math.sin(time * 0.006) * 0.03

    // 移除随机故障效果

    // 恢复道具按钮跳动动画，应用到手套图片上
    if (this.powerUpItem.isAvailable && this.powerUpItem.count > 0) {
      this.powerUpItem.pulseAnimation = 1 + Math.sin(time * 0.008) * 0.15 // 增强跳动效果
    } else {
      this.powerUpItem.pulseAnimation = 1
    }
    this.powerUpItem.glitchEffect = false
  }
  
  /**
   * 初始化背景图片
   */
  async initBackgroundImages() {
    const backgroundConfigs = [
      { level: 1, path: 'game_media/images/bg_1_kindergarten.png', name: '幼儿园场景' },
      { level: 2, path: 'game_media/images/bg_2_tutor.png', name: '家教场景' },
      { level: 3, path: 'game_media/images/bg_3_classroom.png', name: '教室场景' },
      { level: 4, path: 'game_media/images/bg_4_university.png', name: '大学场景' },
      { level: 5, path: 'game_media/images/bg_5_laboratory.png', name: '实验室场景' },
      { level: 6, path: 'game_media/images/bg_6_library.png', name: '图书馆场景' },
      { level: 7, path: 'game_media/images/bg_7_office.png', name: '办公室场景' },
      { level: 8, path: 'game_media/images/bg_8_startup.png', name: '创业公司场景' },
      { level: 9, path: 'game_media/images/bg_9_gov.png', name: '政府机关场景' },
      { level: 10, path: 'game_media/images/bg_10_luxury.png', name: '奢华场景' },
      { level: 11, path: 'game_media/images/bg_11_win.png', name: '胜利场景' }
    ]
    
    // 并行加载所有背景图片
    const loadPromises = backgroundConfigs.map(config => {
      return new Promise((resolve) => {
        try {
          const image = wx.createImage()
          image.onload = () => {
            this.backgroundImages.set(config.level, {
              image: image,
              name: config.name,
              path: config.path,
              loaded: true
            })
            resolve()
          }
          image.onerror = () => {
            console.warn(`背景图片加载失败: ${config.path}`)
            // 即使加载失败也设置一个占位符
            this.backgroundImages.set(config.level, {
              image: null,
              name: config.name,
              path: config.path,
              loaded: false
            })
            resolve()
          }
          image.src = config.path
        } catch (error) {
          console.warn(`背景图片创建失败: ${config.path}`, error)
          resolve()
        }
      })
    })
    
    // 等待所有图片加载完成（或失败）
    await Promise.all(loadPromises)
    
    // 设置初始背景（游戏开始时默认为等级1）
    this.currentBackgroundLevel = 1
    const backgroundData = this.backgroundImages.get(1)
    if (backgroundData && backgroundData.image) {
      this.currentBackgroundImage = backgroundData.image
    }
  }
  
  /**
   * 获取当前游戏中最高等级的球
   * @returns {number} 最高等级
   */
  getHighestLevelInGame() {
    let highestLevel = 1
    for (const fruit of this.gameData.fruits) {
      if (fruit.level > highestLevel) {
        highestLevel = fruit.level
      }
    }
    return highestLevel
  }
  
  /**
   * 根据等级确定背景等级
   * @param {number} level - 角色等级
   * @returns {number} 背景等级
   */
  getBackgroundLevel(level) {
    if (level >= 11) {
      return 11 // 富老炮 - 胜利场景
    } else if (level >= 10) {
      return 10 // 富小哥 - 奢华场景
    } else if (level >= 9) {
      return 9 // 编内人 - 政府机关场景
    } else if (level >= 8) {
      return 8 // 创老板 - 创业公司场景
    } else if (level >= 7) {
      return 7 // 企小工 - 办公室场景
    } else if (level >= 6) {
      return 6 // 博学者 - 图书馆场景
    } else if (level >= 5) {
      return 5 // 研不休 - 实验室场景
    } else if (level >= 4) {
      return 4 // 大满贯 - 大学场景
    } else if (level >= 3) {
      return 3 // 走读生 - 教室场景
    } else if (level >= 2) {
      return 2 // 年轻哥 - 家教场景
    } else {
      return 1 // 小宝 - 幼儿园场景
    }
  }
  
  /**
   * 更新背景图片
   * @param {number} newLevel - 新合成的角色等级
   */
  updateBackground(newLevel) {
    // 获取当前游戏中最高等级
    const currentHighestLevel = this.getHighestLevelInGame()
    
    // 只有当新合成的等级高于当前最高等级时，才更新背景
    if (newLevel > currentHighestLevel) {
      const backgroundLevel = this.getBackgroundLevel(newLevel)
      
      // 如果背景等级发生变化，更新背景
      if (backgroundLevel !== this.currentBackgroundLevel) {
        this.currentBackgroundLevel = backgroundLevel
        const backgroundData = this.backgroundImages.get(backgroundLevel)
        if (backgroundData && backgroundData.image) {
          this.currentBackgroundImage = backgroundData.image
          // console.log(`背景切换到: ${backgroundData.name} (等级 ${backgroundLevel})`)
        }
      }
    }
  }
  
  showLevelUpEffect() {
    wx.showToast({
      title: `升级到 ${this.gameData.currentLevel} 级！`,
      icon: 'success',
      duration: 2000
    })
  }
  
  render(ctx) {
    // 绘制背景
    this.renderBackground(ctx)
    
    // 恢复所有UI元素
    this.renderGameArea(ctx)
    this.renderLaunchArea(ctx)
    this.renderNextFruit(ctx)
    this.renderUI(ctx)
    this.particleSystem.render(ctx)
    
    // 最后绘制所有角色，确保在最上层
    this.gameData.fruits.forEach(fruit => {
      fruit.render(ctx)
    })
    
    // 绘制加载提示（如果可见）
    this.loadingScreen.render(ctx)
  }
  
  renderBackground(ctx) {
    // 绘制背景图片
    if (this.currentBackgroundImage && this.currentBackgroundImage.complete && this.currentBackgroundImage.naturalWidth > 0) {
      // 计算背景图片的绘制参数，保持宽高比
      const imageAspectRatio = this.currentBackgroundImage.naturalWidth / this.currentBackgroundImage.naturalHeight
      const screenAspectRatio = this.screenWidth / this.screenHeight
      
      let drawWidth = this.screenWidth
      let drawHeight = this.screenHeight
      let drawX = 0
      let drawY = 0
      
      if (imageAspectRatio > screenAspectRatio) {
        // 图片较宽，以高度为准
        drawHeight = this.screenHeight
        drawWidth = this.screenHeight * imageAspectRatio
        drawX = (this.screenWidth - drawWidth) / 2
      } else {
        // 图片较高，以宽度为准
        drawWidth = this.screenWidth
        drawHeight = this.screenWidth / imageAspectRatio
        drawY = (this.screenHeight - drawHeight) / 2
      }
      
      // 绘制背景图片
      ctx.drawImage(this.currentBackgroundImage, drawX, drawY, drawWidth, drawHeight)
    } else {
      // 备用背景 - 浅黄色
      ctx.fillStyle = '#FFF8DC' // 浅黄色背景
      ctx.fillRect(0, 0, this.screenWidth, this.screenHeight)
      
      // 绘制网格背景（更淡的颜色）
      ctx.strokeStyle = '#F0E68C' // 淡黄色网格
      ctx.lineWidth = 1
      const gridSize = 30
      
      for (let x = 0; x < this.screenWidth; x += gridSize) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, this.screenHeight)
        ctx.stroke()
      }
      
      for (let y = 0; y < this.screenHeight; y += gridSize) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(this.screenWidth, y)
        ctx.stroke()
      }
    }
  }
  
  renderGameArea(ctx) {
    // 获取警戒线碰撞盒信息（保留用于游戏逻辑）
    const collisionBox = this.getWarningLineCollisionBox()
    
    // 绘制简单的警戒线
    if (this.warningLineContact.isContacting) {
      // 接触时显示红色
      ctx.strokeStyle = '#FF0000'
      ctx.lineWidth = 2
    } else {
      // 正常时显示橙色
      ctx.strokeStyle = '#FFA500'
      ctx.lineWidth = 1
    }
    
    ctx.beginPath()
    ctx.moveTo(0, this.warningLineY)
    ctx.lineTo(this.screenWidth, this.warningLineY)
    ctx.stroke()
  }
  
  renderLaunchArea(ctx) {
    // 这个方法已经不再需要，因为掉落区域在renderGameArea中已经绘制
    // 保留空方法以避免调用错误
  }
  
  renderNextFruit(ctx) {
    if (!this.nextFruit) {
      console.warn('nextFruit is null or undefined')
      return
    }
    
    // 在屏幕顶部显示下一个角色的预览
    const previewX = this.screenWidth / 2
    const previewY = 80 // 屏幕顶部80像素位置
    
    // 绘制预览角色（显示真实头像）
    try {
      // 确保nextFruit有必要的属性
      if (!this.nextFruit.color || !this.nextFruit.radius) {
        throw new Error('nextFruit missing required properties')
      }
      
      // 绘制角色阴影
      ctx.fillStyle = 'rgba(0, 0, 0, 0.2)'
      ctx.beginPath()
      ctx.arc(previewX + 2, previewY + 2, this.nextFruit.radius, 0, Math.PI * 2)
      ctx.fill()
      
      // 渲染真实头像
      this.drawPreviewWithImage(ctx, previewX, previewY)
      
    } catch (error) {
      // 如果创建Fruit对象失败，绘制简单的圆形
      console.warn('Failed to create preview character:', error)
      ctx.fillStyle = this.nextFruit.color || '#FF0000'
      ctx.beginPath()
      ctx.arc(previewX, previewY, this.nextFruit.radius || 20, 0, Math.PI * 2)
      ctx.fill()
    }
  }
  
  /**
   * 使用图片绘制预览角色
   * @param {CanvasRenderingContext2D} ctx - Canvas 2D 上下文
   * @param {number} previewX - 预览X坐标
   * @param {number} previewY - 预览Y坐标
   */
  drawPreviewWithImage(ctx, previewX, previewY) {
    // 绘制角色主体背景
    ctx.fillStyle = this.nextFruit.color
    ctx.beginPath()
    ctx.arc(previewX, previewY, this.nextFruit.radius, 0, Math.PI * 2)
    ctx.fill()
    
    // 绘制角色边框
    ctx.strokeStyle = this.getDarkerColor(this.nextFruit.color)
    ctx.lineWidth = 1
    ctx.stroke()
    
    // 尝试绘制头像图片
    if (this.nextFruit.imagePath) {
      // 优先从ResourceLoader获取已加载的图片
      let image = this.main.resourceLoader.getLoadedImage(this.nextFruit.imagePath)
      
      if (!image) {
        // 如果ResourceLoader中没有，检查本地缓存
        image = this.previewImageCache.get(this.nextFruit.imagePath)
        
        if (!image) {
          // 创建新图片并缓存
          image = wx.createImage()
          image.onload = () => {
            // 图片加载完成，缓存已更新
          }
          image.onerror = () => {
            // 图片加载失败，从缓存中移除
            this.previewImageCache.delete(this.nextFruit.imagePath)
          }
          image.src = this.nextFruit.imagePath
          this.previewImageCache.set(this.nextFruit.imagePath, image)
        }
      }
      
      // 如果图片已加载完成，绘制头像
      if (image.complete && image.naturalWidth > 0) {
        // 计算头像绘制区域 - 调整大小比例
        const avatarRadius = this.nextFruit.radius * 0.9 // 增大头像半径
        const avatarX = previewX - avatarRadius
        const avatarY = previewY - avatarRadius
        const avatarSize = avatarRadius * 2
        
        // 创建圆形裁剪区域
        ctx.save()
        ctx.beginPath()
        ctx.arc(previewX, previewY, avatarRadius, 0, Math.PI * 2)
        ctx.clip()
        
        // 绘制头像 - 保持宽高比
        const imageAspectRatio = image.naturalWidth / image.naturalHeight
        let drawWidth = avatarSize
        let drawHeight = avatarSize
        let drawX = avatarX
        let drawY = avatarY
        
        // 根据图片宽高比调整绘制尺寸
        if (imageAspectRatio > 1) {
          // 图片较宽，调整高度
          drawHeight = avatarSize / imageAspectRatio
          drawY = previewY - drawHeight / 2
        } else if (imageAspectRatio < 1) {
          // 图片较高，调整宽度
          drawWidth = avatarSize * imageAspectRatio
          drawX = previewX - drawWidth / 2
        } else {
          // 正方形图片，居中显示
          drawX = previewX - avatarSize / 2
          drawY = previewY - avatarSize / 2
        }
        
        // 绘制头像
        ctx.drawImage(image, drawX, drawY, drawWidth, drawHeight)
        ctx.restore()
      }
    }
    
    // 绘制角色高光
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.beginPath()
    ctx.arc(previewX - this.nextFruit.radius * 0.3, previewY - this.nextFruit.radius * 0.3, this.nextFruit.radius * 0.3, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 使用颜色绘制预览角色（备用方案）
   * @param {CanvasRenderingContext2D} ctx - Canvas 2D 上下文
   * @param {number} previewX - 预览X坐标
   * @param {number} previewY - 预览Y坐标
   */
  drawPreviewWithColor(ctx, previewX, previewY) {
    // 绘制角色主体
    ctx.fillStyle = this.nextFruit.color
    ctx.beginPath()
    ctx.arc(previewX, previewY, this.nextFruit.radius, 0, Math.PI * 2)
    ctx.fill()
    
    // 绘制角色边框
    ctx.strokeStyle = this.getDarkerColor(this.nextFruit.color)
    ctx.lineWidth = 1
    ctx.stroke()
    
    // 绘制角色高光
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.beginPath()
    ctx.arc(previewX - this.nextFruit.radius * 0.3, previewY - this.nextFruit.radius * 0.3, this.nextFruit.radius * 0.3, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 获取更暗的颜色（用于边框）
   */
  getDarkerColor(color) {
    const hex = color.replace('#', '')
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 40)
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 40)
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 40)
    return `rgb(${r}, ${g}, ${b})`
  }
  
  renderUI(ctx) {
    // 应用像素化设置
    this.pixelEffects.enablePixelation()

    // 绘制Y2K风格菜单按钮
    this.renderY2KMenuButton(ctx)

    // 绘制Y2K风格计分器
    this.renderY2KScoreDisplay(ctx)

    // 绘制Y2K风格游戏道具
    this.renderY2KPowerUpItem(ctx)

    // 绘制Y2K风格操作提示
    this.drawY2KOperationHint(ctx)

    // 绘制像素效果
    this.pixelEffects.render()

    // 如果菜单弹窗打开，绘制弹窗
    if (this.menuModal.isOpen) {
      this.renderY2KMenuModal(ctx)
    }
  }
  
  /**
   * 绘制Y2K风格菜单按钮
   */
  renderY2KMenuButton(ctx) {
    const button = this.menuButton

    // 按钮配置
    const buttonConfig = {
      color: button.color,
      borderColor: button.borderColor,
      isHovered: button.isHovered,
      glitchEffect: button.glitchEffect,
      glassStyle: true // 启用玻璃拟态风格
    }

    // 计算按钮缩放
    const scale = button.pulseAnimation
    const scaledWidth = button.width * scale
    const scaledHeight = button.height * scale

    // 绘制按钮
    this.pixelEffects.drawPixelButton(
      button.x, button.y,
      scaledWidth, scaledHeight,
      buttonConfig
    )

    // 绘制菜单图标 - 使用emoji风格
    const iconSymbol = '☰'
    // 移除故障效果，只使用正常文字渲染
    this.pixelEffects.drawPixelText(
      iconSymbol,
      button.x, button.y,
      Y2KUIConfig.FONTS.PIXEL_BUTTON,
      Y2KUIConfig.COLORS.DIGITAL_WHITE,
      Y2KUIConfig.COLORS.PIXEL_BLACK
    )
  }
  
  /**
   * 绘制计分器
   */
  /**
   * 绘制Y2K风格计分器
   */
  renderY2KScoreDisplay(ctx) {
    const display = this.scoreDisplay

    // 更新动画计时器
    display.animationTimer += 16.67

    // 绘制卡通游戏风格背景面板
    ctx.save()

    // 计算面板位置
    const panelX = display.x - display.width / 2
    const panelY = display.y - display.height / 2
    const cornerRadius = 8 // 圆角半径

    // 绘制阴影（圆角矩形）
    ctx.fillStyle = 'rgba(0, 0, 0, 0.4)'
    this.drawRoundedRect(ctx, panelX + 4, panelY + 4, display.width, display.height, cornerRadius)
    ctx.fill()

    // 绘制渐变背景（圆角矩形）
    const gradient = ctx.createLinearGradient(panelX, panelY, panelX, panelY + display.height)
    gradient.addColorStop(0, '#E8E8E8')    // 银色顶部
    gradient.addColorStop(0.5, '#C0C0C0')  // 银色中部
    gradient.addColorStop(1, '#A8A8A8')    // 深银色底部

    ctx.fillStyle = gradient
    this.drawRoundedRect(ctx, panelX, panelY, display.width, display.height, cornerRadius)
    ctx.fill()

    // 绘制高光效果（圆角矩形）
    const highlightGradient = ctx.createLinearGradient(panelX, panelY, panelX, panelY + display.height * 0.5)
    highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.6)')
    highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

    ctx.fillStyle = highlightGradient
    this.drawRoundedRect(ctx, panelX, panelY, display.width, display.height * 0.5, cornerRadius)
    ctx.fill()

    // 绘制边框（圆角矩形）- 透明玻璃效果
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)'  // 半透明白色边框
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, panelX, panelY, display.width, display.height, cornerRadius)
    ctx.stroke()

    // 绘制内边框高光（圆角矩形）- 银色高光
    ctx.strokeStyle = 'rgba(192, 192, 192, 0.8)'  // 半透明银色内边框
    ctx.lineWidth = 1
    this.drawRoundedRect(ctx, panelX + 1, panelY + 1, display.width - 2, display.height - 2, cornerRadius - 1)
    ctx.stroke()

    ctx.restore()

    // 移除边框和装饰框架

    // 绘制当前分数 - 带故障效果
    const scoreText = `分数: ${this.scoreDisplay.currentScore.toString().padStart(6, '0')}`
    const scoreColor = this.scoreDisplay.currentScore >= 10000 ?
      Y2KUIConfig.COLORS.NEON_PINK : Y2KUIConfig.COLORS.CYBER_CYAN

    // 使用卡通游戏风格字体 - 模仿PLAY!按钮样式
    ctx.save()
    ctx.font = 'bold 18px Arial, sans-serif' // 加粗游戏字体
    ctx.textAlign = 'left' // 左对齐
    ctx.textBaseline = 'middle'

    // 计算左对齐位置
    const leftX = display.x - display.width / 2 + 12
    const textY = display.y - display.height / 4

    // 1. 绘制深色阴影（最底层）
    ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
    ctx.fillText(scoreText, leftX + 3, textY + 3)

    // 2. 绘制厚重的深色描边
    ctx.strokeStyle = '#4A4A4A' // 深灰色描边
    ctx.lineWidth = 6
    ctx.strokeText(scoreText, leftX, textY)

    // 3. 绘制中层描边
    ctx.strokeStyle = '#2C2C2C' // 更深的描边
    ctx.lineWidth = 4
    ctx.strokeText(scoreText, leftX, textY)

    // 4. 绘制内层描边
    ctx.strokeStyle = '#1A1A1A' // 最深的描边
    ctx.lineWidth = 2
    ctx.strokeText(scoreText, leftX, textY)

    // 5. 绘制主文字（白色）
    ctx.fillStyle = '#FFFFFF'
    ctx.fillText(scoreText, leftX, textY)

    // 6. 添加顶部高光效果
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.fillText(scoreText, leftX, textY - 1)

    ctx.restore()

    // 绘制最高分 - 使用与分数相同的字体效果，但字号小一号
    const highScoreText = `最高分: ${this.scoreDisplay.highScore.toString().padStart(6, '0')}`

    ctx.save()
    ctx.font = 'bold 17px Arial, sans-serif' // 比分数小一个字号
    ctx.textAlign = 'left' // 左对齐
    ctx.textBaseline = 'middle'

    // 计算左对齐位置
    const highScoreLeftX = display.x - display.width / 2 + 12
    const highScoreTextY = display.y + display.height / 4

    // 1. 绘制深色阴影（最底层）
    ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
    ctx.fillText(highScoreText, highScoreLeftX + 3, highScoreTextY + 3)

    // 2. 绘制厚重的深色描边
    ctx.strokeStyle = '#4A4A4A' // 深灰色描边
    ctx.lineWidth = 6
    ctx.strokeText(highScoreText, highScoreLeftX, highScoreTextY)

    // 3. 绘制中层描边
    ctx.strokeStyle = '#2C2C2C' // 更深的描边
    ctx.lineWidth = 4
    ctx.strokeText(highScoreText, highScoreLeftX, highScoreTextY)

    // 4. 绘制内层描边
    ctx.strokeStyle = '#1A1A1A' // 最深的描边
    ctx.lineWidth = 2
    ctx.strokeText(highScoreText, highScoreLeftX, highScoreTextY)

    // 5. 绘制主文字（白色）
    ctx.fillStyle = '#FFFFFF'
    ctx.fillText(highScoreText, highScoreLeftX, highScoreTextY)

    // 6. 添加顶部高光效果
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.fillText(highScoreText, highScoreLeftX, highScoreTextY - 1)

    ctx.restore()
  }
  
  /**
   * 绘制计时器（已移除）
   */
  // renderTimer(ctx) {
  //   const timer = this.timer
  //   
  //   // 格式化时间
  //   const minutes = Math.floor(timer.currentTime / 60)
  //   const seconds = timer.currentTime % 60
  //   const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  //   
  //   // 绘制背景
  //   ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
  //   ctx.fillRect(
  //     timer.x - timer.width / 2,
  //     timer.y - timer.height / 2,
  //     timer.width,
  //     timer.height
  //   )
  //   
  //   // 绘制边框
  //   ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)'
  //   ctx.lineWidth = 1
  //   ctx.strokeRect(
  //     timer.x - timer.width / 2,
  //     timer.y - timer.height / 2,
  //     timer.width,
  //     timer.height
  //   )
  //   
  //   // 根据计时器大小调整字体，支持字体缩放
  //   const fontScale = this.getFontScale()
  //   const fontSize = Math.max(12, Math.min(16, timer.height / 2)) * fontScale
  //   
  //   // 绘制时间
  //   ctx.fillStyle = '#FFFFFF'
  //   ctx.font = `bold ${fontSize}px Arial`
  //   ctx.textAlign = 'center'
  //   ctx.textBaseline = 'middle'
  //   ctx.fillText(timeString, timer.x, timer.y)
  // }
  
  /**
   * 绘制Y2K风格游戏道具
   */
  renderY2KPowerUpItem(ctx) {
    const item = this.powerUpItem

    // 按钮配置
    const isActive = item.isAvailable && item.count > 0
    const buttonConfig = {
      color: isActive ? item.color : Y2KUIConfig.COLORS.METAL_SILVER,
      borderColor: isActive ? item.borderColor : Y2KUIConfig.COLORS.PIXEL_BLACK,
      isHovered: item.isHovered && isActive,
      glitchEffect: item.glitchEffect && isActive
    }

    // 计算按钮缩放
    const scale = item.pulseAnimation
    const scaledWidth = item.width * scale
    const scaledHeight = item.height * scale

    // 移除红色方块背景，不绘制按钮背景

    // 绘制手套图标 - 应用跳动效果和发光效果
    if (item.imageLoaded && item.image) {
      // 基础图标大小，应用跳动效果
      const baseIconSize = Math.min(item.width, item.height) * 0.8
      const iconSize = baseIconSize * scale // 应用跳动缩放

      ctx.save()

      if (isActive) {
        // 有道具时添加镭射紫色发光效果
        ctx.shadowColor = '#8A2BE2' // 镭射紫色发光
        ctx.shadowBlur = 25
        ctx.shadowOffsetX = 0
        ctx.shadowOffsetY = 0

        // 绘制白色发光效果
        const time = Date.now()
        const pulseIntensity = 1 + Math.sin(time * 0.01) * 0.3 // 动态脉冲强度

        // 外层强烈白色光晕
        ctx.shadowColor = '#FFFFFF'
        ctx.shadowBlur = 35 * pulseIntensity
        ctx.drawImage(
          item.image,
          item.x - iconSize / 2,
          item.y - iconSize / 2 - item.height / 8,
          iconSize,
          iconSize
        )

        // 中层白光
        ctx.shadowColor = '#F8F8FF'
        ctx.shadowBlur = 25 * pulseIntensity
        ctx.drawImage(
          item.image,
          item.x - iconSize / 2,
          item.y - iconSize / 2 - item.height / 8,
          iconSize,
          iconSize
        )

        // 内层核心白光
        ctx.shadowColor = '#FFFAFA'
        ctx.shadowBlur = 15 * pulseIntensity
        ctx.drawImage(
          item.image,
          item.x - iconSize / 2,
          item.y - iconSize / 2 - item.height / 8,
          iconSize,
          iconSize
        )

        // 最内层纯白光
        ctx.shadowColor = '#FFFFFF'
        ctx.shadowBlur = 8 * pulseIntensity
        ctx.drawImage(
          item.image,
          item.x - iconSize / 2,
          item.y - iconSize / 2 - item.height / 8,
          iconSize,
          iconSize
        )
      } else {
        // 如果不可用，添加灰度滤镜效果
        ctx.filter = 'grayscale(100%) brightness(0.5)'
        ctx.drawImage(
          item.image,
          item.x - iconSize / 2,
          item.y - iconSize / 2 - item.height / 8,
          iconSize,
          iconSize
        )
      }

      ctx.restore()
    } else {
      // 备用图标 - 使用手套emoji并应用跳动效果
      const iconSymbol = '🥊'

      ctx.save()
      if (isActive) {
        // 有道具时显示镭射紫色发光效果和跳动
        ctx.shadowColor = '#8A2BE2' // 镭射紫色发光
        ctx.shadowBlur = 20
        ctx.shadowOffsetX = 0
        ctx.shadowOffsetY = 0

        // 应用跳动效果到字体大小
        const baseFontSize = 32
        const fontSize = baseFontSize * scale
        ctx.font = `bold ${fontSize}px Arial`
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'

        // 绘制白色发光效果
        const time = Date.now()
        const pulseIntensity = 1 + Math.sin(time * 0.01) * 0.3 // 动态脉冲强度

        // 外层强烈白色光晕
        ctx.shadowColor = '#FFFFFF'
        ctx.shadowBlur = 30 * pulseIntensity
        ctx.fillText(iconSymbol, item.x, item.y - item.height / 8)

        // 中层白光
        ctx.shadowColor = '#F8F8FF'
        ctx.shadowBlur = 20 * pulseIntensity
        ctx.fillText(iconSymbol, item.x, item.y - item.height / 8)

        // 内层核心白光
        ctx.shadowColor = '#FFFAFA'
        ctx.shadowBlur = 12 * pulseIntensity
        ctx.fillText(iconSymbol, item.x, item.y - item.height / 8)

        // 最内层纯白光
        ctx.shadowColor = '#FFFFFF'
        ctx.shadowBlur = 6 * pulseIntensity
        ctx.fillText(iconSymbol, item.x, item.y - item.height / 8)
      } else {
        // 无道具时正常显示
        ctx.font = 'bold 28px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillStyle = '#999999'
        ctx.fillText(iconSymbol, item.x, item.y - item.height / 8)
      }
      ctx.restore()
    }

    // 绘制道具名字和数量
    if (item.count > 0) {
      // 绘制道具名字 - 清晰可见的加粗黑色字体
      ctx.save()
      ctx.font = 'bold 12px Arial'
      ctx.fillStyle = '#000000' // 加粗黑色
      ctx.strokeStyle = '#FFFFFF' // 白色描边增强可读性
      ctx.lineWidth = 2
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'

      const nameText = '霸总的爱'
      ctx.strokeText(nameText, item.x, item.y + item.height / 3)
      ctx.fillText(nameText, item.x, item.y + item.height / 3)

      // 绘制数量
      const countText = `x${item.count}`
      ctx.font = 'bold 10px Arial'
      ctx.strokeText(countText, item.x, item.y + item.height / 2 + 5)
      ctx.fillText(countText, item.x, item.y + item.height / 2 + 5)

      ctx.restore()
    } else {
      // 显示"无可用"
      ctx.save()
      ctx.font = 'bold 10px Arial'
      ctx.fillStyle = '#666666'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText('无可用', item.x, item.y + item.height / 3)
      ctx.restore()
    }
  }

  /**
   * 绘制Y2K风格操作提示
   */
  drawY2KOperationHint(ctx) {
    // 移除操作提示
    return
  }

  /**
   * 绘制圆角矩形路径
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }
  
  /**
   * 绘制Y2K风格菜单弹窗
   */
  renderY2KMenuModal(ctx) {
    const modal = this.menuModal

    // 绘制半透明背景遮罩
    ctx.fillStyle = Y2KUIConfig.COLORS.ALPHA.BACKGROUND_OVERLAY
    ctx.fillRect(0, 0, this.screenWidth, this.screenHeight)

    // 绘制玻璃拟态弹窗背景
    const modalConfig = {
      color: Y2KUIConfig.COLORS.PIXEL_BLACK,
      borderColor: Y2KUIConfig.COLORS.CYBER_CYAN,
      isHovered: false,
      glassStyle: true // 启用玻璃拟态风格
    }

    this.pixelEffects.drawPixelButton(
      modal.x, modal.y,
      modal.width, modal.height,
      modalConfig
    )

    // 移除边框和装饰框架

    // 移除标题

    // 绘制按钮
    for (const button of modal.buttons) {
      this.renderY2KModalButton(ctx, button)
    }
  }

  /**
   * 绘制Y2K风格弹窗按钮
   */
  renderY2KModalButton(ctx, button) {
    // 按钮配置
    const buttonConfig = {
      color: button.color,
      borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
      isHovered: button.isHovered,
      glassStyle: true // 启用玻璃拟态风格
    }

    // 绘制按钮
    this.pixelEffects.drawPixelButton(
      button.x, button.y,
      button.width, button.height,
      buttonConfig
    )

    // 绘制按钮文字
    const buttonText = `> ${button.text}`
    this.pixelEffects.drawPixelText(
      buttonText,
      button.x, button.y,
      Y2KUIConfig.FONTS.PIXEL_BUTTON,
      'rgba(255, 255, 255, 0.9)', // 玻璃风格文字颜色
      null, // 不需要描边
      true, // 启用玻璃风格
      button.isHovered // 传递悬停状态
    )

    // 悬停效果
    if (button.isHovered) {
      this.cyberDecorations.drawDecorativeFrame(
        button.x - button.width / 2 - 5,
        button.y - button.height / 2 - 5,
        button.width + 10,
        button.height + 10
      )
    }
  }
  
  getMaxCharacterHeight() {
    if (this.gameData.fruits.length === 0) return 0
    
    let maxHeight = 0
    for (let fruit of this.gameData.fruits) {
      const fruitTop = fruit.y - fruit.radius
      if (fruitTop < maxHeight || maxHeight === 0) {
        maxHeight = fruitTop
      }
    }
    
    return this.screenHeight - maxHeight
  }

  /**
   * 更新警戒线位置
   */
  updateWarningLinePosition() {
    // 警戒线位置：出球点下方一定距离
    const dropPointY = 50 + 30 // 出球点Y坐标（假设最大角色半径为30）
    const warningLineOffset = 120 // 警戒线距离出球点的偏移量
    this.warningLineY = dropPointY + warningLineOffset
    
    // 确保警戒线不会太靠近屏幕底部
    const minDistanceFromBottom = 50
    const maxWarningLineY = this.screenHeight - minDistanceFromBottom
    this.warningLineY = Math.min(this.warningLineY, maxWarningLineY)
    
    // console.log(`警戒线位置更新: ${this.warningLineY} (出球点: ${dropPointY})`)
  }
  
  /**
   * 打开菜单弹窗
   */
  openMenuModal() {
    this.menuModal.isOpen = true
    this.isPaused = true
    
    // 计算弹窗尺寸和位置
    const modalWidth = Math.min(300, this.screenWidth * 0.8)
    const modalHeight = 280
    this.menuModal.x = this.screenWidth / 2
    this.menuModal.y = this.screenHeight / 2
    this.menuModal.width = modalWidth
    this.menuModal.height = modalHeight
    
    // 创建菜单按钮
    const buttonWidth = modalWidth - 40
    const buttonHeight = 45
    const buttonSpacing = 15
    const startY = this.menuModal.y - modalHeight / 2 + 60
    
    this.menuModal.buttons = [
      {
        id: 'continue',
        text: '继续游戏',
        x: this.menuModal.x,
        y: startY,
        width: buttonWidth,
        height: buttonHeight,
        color: '#27AE60',
        hoverColor: '#229954',
        isHovered: false
      },
      {
        id: 'restart',
        text: '重新开始',
        x: this.menuModal.x,
        y: startY + buttonHeight + buttonSpacing,
        width: buttonWidth,
        height: buttonHeight,
        color: '#F39C12',
        hoverColor: '#E67E22',
        isHovered: false
      },
      {
        id: 'audio',
        text: this.main.audioManager.isMuted ? '音乐开启' : '音乐关闭',
        x: this.menuModal.x,
        y: startY + (buttonHeight + buttonSpacing) * 2,
        width: buttonWidth,
        height: buttonHeight,
        color: '#9B59B6',
        hoverColor: '#8E44AD',
        isHovered: false
      },
      {
        id: 'home',
        text: '返回首页',
        x: this.menuModal.x,
        y: startY + (buttonHeight + buttonSpacing) * 3,
        width: buttonWidth,
        height: buttonHeight,
        color: '#E74C3C',
        hoverColor: '#C0392B',
        isHovered: false
      }
    ]
  }
  
  /**
   * 关闭菜单弹窗
   */
  closeMenuModal() {
    this.menuModal.isOpen = false
    this.isPaused = false
  }
  
  /**
   * 检查是否点击了菜单弹窗
   */
  isPointInMenuModal(x, y) {
    if (!this.menuModal.isOpen) return false
    
    return x >= this.menuModal.x - this.menuModal.width / 2 &&
           x <= this.menuModal.x + this.menuModal.width / 2 &&
           y >= this.menuModal.y - this.menuModal.height / 2 &&
           y <= this.menuModal.y + this.menuModal.height / 2
  }
  
  /**
   * 检查菜单按钮点击
   */
  checkMenuButtonClick(x, y) {
    if (!this.menuModal.isOpen) return null
    
    for (const button of this.menuModal.buttons) {
      if (x >= button.x - button.width / 2 &&
          x <= button.x + button.width / 2 &&
          y >= button.y - button.height / 2 &&
          y <= button.y + button.height / 2) {
        return button
      }
    }
    return null
  }
  
  /**
   * 处理菜单按钮点击
   */
  handleMenuButtonClick(button) {
    this.main.audioManager.playClickSound()
    
    switch (button.id) {
      case 'continue':
        this.closeMenuModal()
        break
        
      case 'restart':
        this.restartGame()
        break
        
      case 'audio':
        this.toggleAudio()
        // 更新按钮文本
        button.text = this.main.audioManager.isMuted ? '音乐开启' : '音乐关闭'
        break
        
      case 'home':
        this.saveGameProgress()
        this.cleanupGameState()
        this.main.switchScene('menu')
        break
    }
  }
  
  /**
   * 重新开始游戏
   */
  restartGame() {
    this.closeMenuModal()
    
    // 重置游戏状态
    this.gameData.reset()
    this.isStarted = false
    this.isPaused = false
    
    // 重新开始游戏
    this.start()
  }
  
  /**
   * 切换音频状态
   */
  toggleAudio() {
    this.main.audioManager.toggleMute()
    
    // 显示状态提示
    const isMuted = this.main.audioManager.isMuted
    wx.showToast({
      title: isMuted ? '音频已关闭' : '音频已开启',
      icon: 'none',
      duration: 1000
    })
  }
  
  /**
   * 保存游戏进度
   */
  saveGameProgress() {
    // 保存当前游戏状态
    try {
      const gameProgress = {
        score: this.gameData.score,
        highScore: this.gameData.highScore,
        // 移除计时器相关数据，因为计时器已被移除
        // gameTime: this.timer.currentTime,
        timestamp: Date.now()
      }
      wx.setStorageSync('gameProgress', gameProgress)
    } catch (e) {
      console.error('保存游戏进度失败:', e)
    }
  }
  
  /**
   * 获取安全区域信息
   */
  getSafeArea() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    
    // 计算安全区域
    const safeArea = {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0
    }
    
    // 适配iPhone X系列的刘海和Home指示器
    if (systemInfo.safeArea) {
      safeArea.top = systemInfo.safeArea.top
      safeArea.bottom = systemInfo.screenHeight - systemInfo.safeArea.bottom
      safeArea.left = systemInfo.safeArea.left
      safeArea.right = systemInfo.screenWidth - systemInfo.safeArea.right
    }
    
    // 适配不同屏幕比例
    const aspectRatio = systemInfo.screenWidth / systemInfo.screenHeight
    
    // 根据屏幕比例调整安全区域
    if (aspectRatio >= 2) { // 18:9, 19.5:9等超宽屏
      safeArea.top = Math.max(safeArea.top, 30)
      safeArea.bottom = Math.max(safeArea.bottom, 20)
    } else if (aspectRatio >= 1.7) { // 16:9
      safeArea.top = Math.max(safeArea.top, 20)
      safeArea.bottom = Math.max(safeArea.bottom, 15)
    } else { // 4:3等传统比例
      safeArea.top = Math.max(safeArea.top, 10)
      safeArea.bottom = Math.max(safeArea.bottom, 10)
    }
    
    return safeArea
  }
  
  /**
   * 获取字体缩放比例
   */
  getFontScale() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      // 根据系统字体大小设置调整缩放比例
      const fontSizeSetting = systemInfo.fontSizeSetting || 'normal'
      
      switch (fontSizeSetting) {
        case 'large':
          return 1.2
        case 'extra-large':
          return 1.4
        case 'small':
          return 0.8
        case 'extra-small':
          return 0.6
        default:
          return 1.0
      }
    } catch (e) {
      return 1.0
    }
  }

  /**
   * 计算UI元素位置（基于警戒线位置）
   */
  calculateUIPositions() {
    // 获取安全区域
    const safeArea = this.getSafeArea()
    
    // 计算警戒线上方的可用空间
    const availableHeight = this.warningLineY - safeArea.top - 20 // 考虑安全区域
    const uiMargin = 15 // UI元素之间的间距
    const leftMargin = Math.max(20, safeArea.left) // 考虑安全区域
    const rightMargin = Math.max(20, safeArea.right) // 考虑安全区域
    
    // 计算每个UI元素的高度总和（菜单按钮和计分器）
    const totalUIHeight = this.menuButton.height + this.scoreDisplay.height + uiMargin * 2

    // 如果UI元素总高度超过可用空间，调整大小
    if (totalUIHeight > availableHeight) {
      const scaleFactor = availableHeight / totalUIHeight
      this.menuButton.width *= scaleFactor
      this.menuButton.height *= scaleFactor
      this.scoreDisplay.width *= scaleFactor
      this.scoreDisplay.height *= scaleFactor
    }

    // 计算起始Y位置（整体下移，更靠近警戒线）
    const originalStartY = safeArea.top + 30 + this.menuButton.height / 2
    const additionalOffset = 60 // 额外向下偏移量，使UI更靠近警戒线
    const startY = originalStartY + additionalOffset

    // 设置菜单按钮位置（左上角）
    this.menuButton.x = leftMargin + this.menuButton.width / 2
    this.menuButton.y = startY

    // 设置计分器位置（菜单按钮上方）
    this.scoreDisplay.x = leftMargin + this.scoreDisplay.width / 2
    this.scoreDisplay.y = this.menuButton.y - this.menuButton.height / 2 - this.scoreDisplay.height / 2 - uiMargin
    
    // 设置计时器位置（已移除）
    // this.timer.x = leftMargin + this.timer.width / 2
    // this.timer.y = this.scoreDisplay.y + this.scoreDisplay.height / 2 + 
    //               this.timer.height / 2 + uiMargin
    
    // 设置道具位置（右上角，与菜单按钮平行，但向上移动20px）
    this.powerUpItem.x = this.screenWidth - rightMargin - this.powerUpItem.width / 2
    this.powerUpItem.y = startY - 20

    // 确保道具按钮与菜单按钮之间有足够间距
    const minDistance = 20
    const buttonDistance = this.powerUpItem.x - this.menuButton.x - this.menuButton.width / 2 - this.powerUpItem.width / 2
    if (buttonDistance < minDistance) {
      // 如果水平空间不够，将道具按钮放在菜单按钮下方（也向上移动20px）
      this.powerUpItem.x = this.screenWidth - rightMargin - this.powerUpItem.width / 2
      this.powerUpItem.y = this.menuButton.y + this.menuButton.height / 2 + this.powerUpItem.height / 2 + uiMargin - 20
    }
  }

  /**
   * 获取警戒线精确碰撞盒信息
   * @returns {Object} 警戒线碰撞盒信息
   */
  getWarningLineCollisionBox() {
    const warningLineY = this.warningLineY
    const warningLineThickness = this.warningLineContact.isContacting ? 5 : 3
    const warningLineHalfThickness = warningLineThickness / 2
    
    return {
      x: 0,
      y: warningLineY,
      width: this.screenWidth,
      height: warningLineThickness,
      top: warningLineY - warningLineHalfThickness,
      bottom: warningLineY + warningLineHalfThickness,
      isContacting: this.warningLineContact.isContacting,
      contactTime: this.warningLineContact.isContacting ? 
        Date.now() - this.warningLineContact.contactStartTime : 0
    }
  }
  
  /**
   * 检查角色是否与警戒线碰撞盒接触
   * @param {Fruit} fruit - 角色对象
   * @returns {boolean} 是否接触
   */
  isFruitContactingWarningLine(fruit) {
    const collisionBox = this.getWarningLineCollisionBox()
    
    const fruitTop = fruit.y - fruit.radius
    const fruitBottom = fruit.y + fruit.radius
    
    return (
      (fruitBottom >= collisionBox.top && fruitBottom <= collisionBox.bottom) ||
      (fruitTop <= collisionBox.bottom && fruitTop >= collisionBox.top) ||
      (fruitTop <= collisionBox.top && fruitBottom >= collisionBox.bottom)
    )
  }
  

} 