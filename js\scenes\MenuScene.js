import { Y2KUIConfig } from '../config/Y2KUIConfig.js'
import { PixelEffects } from '../utils/PixelEffects.js'
import { GameData } from '../utils/GameData.js'
import { CyberDecorations } from '../utils/CyberDecorations.js'

export class MenuScene {
  constructor(main) {
    this.main = main
    this.screenWidth = main.screenWidth
    this.screenHeight = main.screenHeight

    // Y2K UI系统
    this.pixelEffects = new PixelEffects(main.canvas, main.ctx)
    this.cyberDecorations = new CyberDecorations(main.canvas, main.ctx)

    // 动画相关
    this.animationTime = 0
    this.titleGlitchTimer = 0
    this.backgroundAnimOffset = 0

    // 按钮配置 - Y2K风格重新设计
    this.buttons = [
      {
        text: '> 启动系统',
        x: this.screenWidth / 2,
        y: this.screenHeight * 0.48,
        width: Y2KUIConfig.DIMENSIONS.BUTTON_PRIMARY.width,
        height: Y2KUIConfig.DIMENSIONS.BUTTON_PRIMARY.height,
        color: Y2KUIConfig.COLORS.CYBER_CYAN,
        gradient: Y2KUIConfig.COLORS.GRADIENTS.CYBER_BLUE,
        borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
        isMain: true,
        pulseAnimation: 1,
        isHovered: false,
        glitchEffect: false
      },
      {
        text: '> 系统手册',
        x: this.screenWidth / 2,
        y: this.screenHeight * 0.61,
        width: Y2KUIConfig.DIMENSIONS.BUTTON_SECONDARY.width + 80,
        height: Y2KUIConfig.DIMENSIONS.BUTTON_SECONDARY.height + 10,
        color: Y2KUIConfig.COLORS.MATRIX_GREEN,
        gradient: Y2KUIConfig.COLORS.GRADIENTS.MATRIX_FLOW,
        borderColor: Y2KUIConfig.COLORS.MATRIX_GREEN,
        isMain: false,
        pulseAnimation: 1,
        isHovered: false,
        glitchEffect: false
      },
      {
        text: '> 成就一览',
        x: this.screenWidth / 2,
        y: this.screenHeight * 0.74,
        width: Y2KUIConfig.DIMENSIONS.BUTTON_SECONDARY.width + 80,
        height: Y2KUIConfig.DIMENSIONS.BUTTON_SECONDARY.height + 10,
        color: Y2KUIConfig.COLORS.ELECTRIC_PURPLE,
        gradient: Y2KUIConfig.COLORS.GRADIENTS.NEON_GLOW,
        borderColor: Y2KUIConfig.COLORS.ELECTRIC_PURPLE,
        isMain: false,
        pulseAnimation: 1,
        isHovered: false,
        glitchEffect: false
      },
      {
        text: '> 分享游戏',
        x: this.screenWidth / 2,
        y: this.screenHeight * 0.87,
        width: Y2KUIConfig.DIMENSIONS.BUTTON_SECONDARY.width + 80,
        height: Y2KUIConfig.DIMENSIONS.BUTTON_SECONDARY.height + 10,
        color: Y2KUIConfig.COLORS.LIQUID_ORANGE,
        gradient: [Y2KUIConfig.COLORS.LIQUID_ORANGE, '#CC5500'],
        borderColor: Y2KUIConfig.COLORS.LIQUID_ORANGE,
        isMain: false,
        pulseAnimation: 1,
        isHovered: false,
        glitchEffect: false
      }
    ]

    this.hoveredButton = null
    this.buttonHoverTime = 0

    // 自定义弹窗相关
    this.showModal = false
    this.modalContent = ''
    this.modalTitle = ''
    this.modalButtonText = '> 关闭'
    this.modalScrollY = 0
    this.modalMaxScrollY = 0
    this.isScrolling = false
    this.scrollStartY = 0
    this.modalType = 'rules' // 'rules' 或 'achievements'

    // 音频控制按钮 - Y2K风格
    this.audioButton = {
      x: 70,
      y: 70,
      width: Y2KUIConfig.DIMENSIONS.BUTTON_ICON.width,
      height: Y2KUIConfig.DIMENSIONS.BUTTON_ICON.height,
      color: Y2KUIConfig.COLORS.MATRIX_GREEN,
      hoverColor: Y2KUIConfig.COLORS.CYBER_CYAN,
      mutedColor: Y2KUIConfig.COLORS.NEON_PINK,
      mutedHoverColor: '#FF0040',
      borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
      isHovered: false,
      pulseAnimation: 1
    }

    // 初始化游戏数据以获取角色配置
    this.gameData = new GameData()

    // 不再直接绑定事件，由事件管理器统一处理
  }
  
  /**
   * 初始化粒子效果
   */
  initParticles() {
    for (let i = 0; i < 50; i++) {
      this.particles.push({
        x: Math.random() * this.screenWidth,
        y: Math.random() * this.screenHeight,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        size: Math.random() * 3 + 1,
        opacity: Math.random() * 0.5 + 0.2,
        color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'][Math.floor(Math.random() * 5)]
      })
    }
  }

  /**
   * 加载背景图片
   */
  loadBackgroundImage() {
    try {
      this.backgroundImage = wx.createImage()
      this.backgroundImage.onload = () => {
        this.backgroundImageLoaded = true
        console.log('菜单背景图片加载成功: 11.png')
      }
      this.backgroundImage.onerror = (error) => {
        console.warn('菜单背景图片加载失败: 11.png', error)
        this.backgroundImageLoaded = false
      }
      this.backgroundImage.src = 'game_media/images/11.png'
    } catch (error) {
      console.warn('创建菜单背景图片对象失败: 11.png', error)
      this.backgroundImageLoaded = false
    }
  }


  
  /**
   * 处理触摸开始事件（由事件管理器调用）
   */
  handleTouchStart(x, y) {
    // 检查音频控制按钮点击
    if (x >= this.audioButton.x - this.audioButton.width / 2 && 
        x <= this.audioButton.x + this.audioButton.width / 2 &&
        y >= this.audioButton.y - this.audioButton.height / 2 && 
        y <= this.audioButton.y + this.audioButton.height / 2) {
      this.main.audioManager.toggleMute()
      
      // 显示状态提示
      const isMuted = this.main.audioManager.isMuted
      wx.showToast({
        title: isMuted ? '音频已关闭' : '音频已开启',
        icon: 'none',
        duration: 1000
      })
      
      // 注意：这里不播放点击音效，因为用户可能正在关闭声音
      return
    }
    
    // 如果弹窗显示，检查是否点击了弹窗按钮
    if (this.showModal) {
      // 使用与渲染时相同的弹窗尺寸计算 - 响应式尺寸
      const modalWidth = Math.min(Y2KUIConfig.DIMENSIONS.MODAL_PANEL.width, this.screenWidth * 0.9)
      const modalHeight = Math.min(Y2KUIConfig.DIMENSIONS.MODAL_PANEL.height, this.screenHeight * 0.8)
      const modalX = (this.screenWidth - modalWidth) / 2
      const modalY = (this.screenHeight - modalHeight) / 2
      const buttonY = modalY + modalHeight - 60

      const modalButton = {
        x: this.screenWidth / 2,
        y: buttonY,
        width: 200,
        height: 50
      }

      if (this.isPointInButton(x, y, modalButton)) {
        this.showModal = false
        // 所有弹窗按钮都只是关闭弹窗，返回首页
        return
      }

      // 检查是否在弹窗内容区域内
      const contentX = modalX + 20
      const contentY = modalY + 80
      const contentWidth = modalWidth - 40
      const contentHeight = modalHeight - 160

      if (x >= contentX && x <= contentX + contentWidth &&
          y >= contentY && y <= contentY + contentHeight) {
        this.isScrolling = true
        this.scrollStartY = y
        return
      }

      // 如果点击了弹窗区域外，关闭弹窗
      this.showModal = false
      return
    }
    
    for (let i = 0; i < this.buttons.length; i++) {
      const button = this.buttons[i]
      if (this.isPointInButton(x, y, button)) {
        this.onButtonClick(i)
        break
      }
    }
  }
  
  /**
   * 处理触摸移动事件（由事件管理器调用）
   */
  handleTouchMove(x, y) {
    // 处理弹窗滚动
    if (this.showModal && this.isScrolling) {
      const deltaY = this.scrollStartY - y
      this.modalScrollY = Math.max(0, Math.min(this.modalMaxScrollY, this.modalScrollY + deltaY))
      this.scrollStartY = y
    }
  }
  
  /**
   * 处理触摸结束事件（由事件管理器调用）
   */
  handleTouchEnd() {
    // 结束滚动状态
    this.isScrolling = false
  }
  
  /**
   * 清理事件监听（场景切换时调用）
   */
  cleanupEvents() {
    // 菜单场景没有需要清理的事件监听
  }
  
  isPointInButton(x, y, button) {
    return x >= button.x - button.width / 2 &&
           x <= button.x + button.width / 2 &&
           y >= button.y - button.height / 2 &&
           y <= button.y + button.height / 2
  }
  
  onButtonClick(buttonIndex) {
    // 播放点击音效
    this.main.audioManager.playClickSound()
    
    // 添加震动反馈
    if (wx.vibrateShort) {
      wx.vibrateShort()
    }
    
    switch (buttonIndex) {
      case 0: // 开始游戏
        this.main.switchScene('game')
        break
      case 1: // 游戏规则
        this.showInstructions()
        break
      case 2: // 成就介绍
        this.showAchievements()
        break
      case 3: // 分享游戏
        this.shareToWeChat('friend')
        break
    }
  }
  
  showInstructions() {
    this.modalType = 'rules'
    this.modalTitle = '🎮 游戏玩法介绍'
    this.modalContent = `21世纪进化论是一款物理合成类游戏，玩法简单有趣：

1. 投放角色：点击屏幕任意位置，将角色投放到游戏区域
2. 合成进化：两个相同的角色碰撞后会合成为更高级的角色
3. 获得分数：每次合成都会获得相应的分数奖励
4. 避免失败：角色堆积超过红线1.5秒后游戏结束
5. 终极目标：合成出最高级角色"富老炮"即可通关

👥 角色介绍

基础角色（游戏开始时随机投放）
CHAR_DISPLAY_SECTION_BASIC

进阶角色（通过合成获得）
CHAR_DISPLAY_SECTION_ADVANCED

特殊角色
CHAR_DISPLAY_SECTION_SPECIAL

🎯 特殊机制

富小哥的特殊能力：合成时有90%概率变成丐帮主，10%概率直接变成富老炮
丐帮主的妙用：点击丐帮主可将其转换为小角色，获得666分奖励
霸总的爱：游戏中的特殊道具，可以帮助您获得更多分数

🏆 游戏成就

游戏设有多种成就等待您来解锁：

初出茅庐：完成第一次合成
小有成就：累计得分达到100分
事业有成：累计得分达到1000分
财富自由：累计得分达到10000分
人生巅峰：成功合成富老炮
合成大师：完成100次合成
时间管理：单局游戏时间超过5分钟
速战速决：3分钟内达到1000分

💡 游戏技巧

1. 合理规划：观察角色分布，规划合成路线
2. 控制节奏：不要急于投放，等待最佳时机
3. 善用丐帮主：适时点击丐帮主可以缓解空间压力
4. 背景变化：随着最高等级角色的出现，游戏背景会相应变化

祝您游戏愉快，早日成为人生赢家！🎉`
    this.modalButtonText = '知道了！'
    this.showModal = true
    this.modalScrollY = 0
    this.calculateModalScroll()
  }
  
  showAchievements() {
    this.modalType = 'achievements'
    this.modalTitle = '🏆 成就系统'
    this.modalContent = `## 🎯 游戏成就

游戏设有多种成就等待您来解锁，每个成就都有独特的奖励和意义：

### 🥇 基础成就
- **初出茅庐**：完成第一次合成
- **小有成就**：累计得分达到100分
- **事业有成**：累计得分达到1000分
- **财富自由**：累计得分达到10000分

### 🏅 高级成就
- **人生巅峰**：成功合成富老炮
- **合成大师**：完成100次合成
- **时间管理**：单局游戏时间超过5分钟
- **速战速决**：3分钟内达到1000分

## 💎 成就奖励

解锁成就不仅能获得成就感，还能：
- 提升游戏体验
- 解锁特殊功能
- 获得隐藏奖励
- 展示游戏实力

## 🎮 解锁条件

每个成就都有明确的解锁条件，挑战自己，突破极限！

继续游戏，解锁更多成就吧！🌟`
    this.modalButtonText = '继续游戏'
    this.showModal = true
    this.modalScrollY = 0
    this.calculateModalScroll()
  }
  

  
  /**
   * 计算弹窗滚动范围
   */
  calculateModalScroll() {
    // 使用响应式弹窗尺寸
    const modalWidth = Math.min(Y2KUIConfig.DIMENSIONS.MODAL_PANEL.width, this.screenWidth * 0.9)
    const modalHeight = Math.min(Y2KUIConfig.DIMENSIONS.MODAL_PANEL.height, this.screenHeight * 0.8)
    const contentHeight = modalHeight - 160 // 减去标题和按钮区域

    if (this.modalType === 'achievements') {
      // 成就弹窗的高度计算
      const cardHeight = 80 // 更新后的卡片高度
      const cardSpacing = 15 // 更新后的间距
      const titleHeight = 50 // 标题区域高度
      const achievements = [
        { id: 'first_merge', name: '初出茅庐', desc: '完成第一次合成', unlocked: this.checkAchievement('first_merge') },
        { id: 'score_100', name: '小有成就', desc: '累计得分达到100分', unlocked: this.checkAchievement('score_100') },
        { id: 'score_1000', name: '事业有成', desc: '累计得分达到1000分', unlocked: this.checkAchievement('score_1000') },
        { id: 'score_10000', name: '财富自由', desc: '累计得分达到10000分', unlocked: this.checkAchievement('score_10000') },
        { id: 'rich_old_man', name: '人生巅峰', desc: '成功合成富老炮', unlocked: this.checkAchievement('rich_old_man') },
        { id: 'merge_100', name: '合成大师', desc: '完成100次合成', unlocked: this.checkAchievement('merge_100') },
        { id: 'time_5min', name: '时间管理', desc: '单局游戏时间超过5分钟', unlocked: this.checkAchievement('time_5min') },
        { id: 'speed_1000', name: '速战速决', desc: '3分钟内达到1000分', unlocked: this.checkAchievement('speed_1000') }
      ]

      const totalHeight = titleHeight + achievements.length * (cardHeight + cardSpacing) + 80
      this.modalMaxScrollY = Math.max(0, totalHeight - contentHeight)
    } else {
      // 规则弹窗的高度计算 - 考虑角色展示和更大字体
      const baseLineHeight = Math.max(20, this.screenWidth * 0.045) // 增大基础行高
      const lines = this.modalContent.split('\n')
      let totalHeight = 15 // 顶部间距

      for (let line of lines) {
        if (line.trim() === '') {
          totalHeight += baseLineHeight * 0.5 // 空行
        } else if (line.includes('CHAR_DISPLAY_SECTION_')) {
          // 角色展示部分需要更多空间
          if (line.includes('BASIC')) {
            totalHeight += 3 * (35 + 15) // 3个基础角色
          } else if (line.includes('ADVANCED')) {
            totalHeight += 7 * (35 + 15) // 7个进阶角色
          } else if (line.includes('SPECIAL')) {
            totalHeight += 2 * (35 + 15) // 2个特殊角色
          }
        } else if (line.startsWith('##')) {
          totalHeight += baseLineHeight * 1.2 // 二级标题
        } else if (line.startsWith('#')) {
          totalHeight += baseLineHeight * 1.3 // 一级标题
        } else {
          // 普通文本 - 考虑更大字体的换行
          const estimatedLines = Math.ceil(line.length / 45) // 调整换行估算
          totalHeight += Math.max(1, estimatedLines) * baseLineHeight
        }
      }

      // 添加底部间距 - 确保按钮不会遮挡文字，增加更多空间
      totalHeight += 120

      this.modalMaxScrollY = Math.max(0, totalHeight - contentHeight)
    }
  }
  
  /**
   * 文字换行处理
   */
  wrapText(ctx, text, maxWidth) {
    if (!text) return ['']
    
    // 如果没有ctx，使用估算的字符宽度
    const charWidth = ctx ? null : 16 // 估算每个字符的宽度
    
    const words = text.split('')
    const lines = []
    let currentLine = ''
    
    for (let i = 0; i < words.length; i++) {
      const word = words[i]
      const testLine = currentLine + word
      
      let testWidth
      if (ctx) {
        testWidth = ctx.measureText(testLine).width
      } else {
        testWidth = testLine.length * charWidth
      }
      
      if (testWidth > maxWidth && currentLine !== '') {
        lines.push(currentLine)
        currentLine = word
      } else {
        currentLine = testLine
      }
    }
    
    if (currentLine !== '') {
      lines.push(currentLine)
    }
    
    return lines.length > 0 ? lines : ['']
  }
  
  /**
   * 渲染规则内容
   */
  renderRulesContent(ctx, contentX, contentY, contentWidth, contentHeight) {
    // 绘制内容文字
    ctx.fillStyle = '#BDC3C7'
    ctx.font = '16px Arial'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'top'
    
    // 文字换行处理
    const lines = this.modalContent.split('\n')
    let currentY = contentY + 30 - this.modalScrollY
    const lineHeight = 22
    const maxLineWidth = contentWidth - 40 // 为左右边距留出空间
    
    for (let line of lines) {
      if (line.trim() === '') {
        currentY += lineHeight / 2
        continue
      }
      
      // 跳过超出上边界的行
      if (currentY + lineHeight < contentY) {
        currentY += lineHeight
        continue
      }
      
      // 处理标题行
      if (line.startsWith('##')) {
        ctx.fillStyle = '#F39C12'
        ctx.font = 'bold 20px Arial'
        const titleText = line.replace('## ', '')
        const wrappedLines = this.wrapText(ctx, titleText, maxLineWidth)
        for (let wrappedLine of wrappedLines) {
          if (currentY + lineHeight < contentY) {
            currentY += lineHeight
            continue
          }
          ctx.fillText(wrappedLine, contentX + 20, currentY)
          currentY += lineHeight
          if (currentY > contentY + contentHeight - 20) break
        }
        currentY += 5
        ctx.fillStyle = '#BDC3C7'
        ctx.font = '16px Arial'
        continue
      }
      
      if (line.startsWith('###')) {
        ctx.fillStyle = '#E74C3C'
        ctx.font = 'bold 18px Arial'
        const subtitleText = line.replace('### ', '')
        const wrappedLines = this.wrapText(ctx, subtitleText, maxLineWidth)
        for (let wrappedLine of wrappedLines) {
          if (currentY + lineHeight < contentY) {
            currentY += lineHeight
            continue
          }
          ctx.fillText(wrappedLine, contentX + 20, currentY)
          currentY += lineHeight
          if (currentY > contentY + contentHeight - 20) break
        }
        currentY += 3
        ctx.fillStyle = '#BDC3C7'
        ctx.font = '16px Arial'
        continue
      }
      
      // 处理普通行
      if (line.startsWith('- **') || line.startsWith('1. **')) {
        ctx.fillStyle = '#3498DB'
        ctx.font = 'bold 16px Arial'
      } else if (line.startsWith('- **')) {
        ctx.fillStyle = '#2ECC71'
        ctx.font = 'bold 16px Arial'
      } else {
        ctx.fillStyle = '#BDC3C7'
        ctx.font = '16px Arial'
      }
      
      // 对普通行进行换行处理
      const wrappedLines = this.wrapText(ctx, line, maxLineWidth)
      for (let wrappedLine of wrappedLines) {
        if (currentY + lineHeight < contentY) {
          currentY += lineHeight
          continue
        }
        ctx.fillText(wrappedLine, contentX + 20, currentY)
        currentY += lineHeight
        if (currentY > contentY + contentHeight - 20) break
      }
    }
  }
  
  /**
   * 渲染成就内容
   */
  renderAchievementsContent(ctx, contentX, contentY, contentWidth, contentHeight) {
    const achievements = [
      { id: 'first_merge', name: '初出茅庐', desc: '完成第一次合成', unlocked: this.checkAchievement('first_merge') },
      { id: 'score_100', name: '小有成就', desc: '累计得分达到100分', unlocked: this.checkAchievement('score_100') },
      { id: 'score_1000', name: '事业有成', desc: '累计得分达到1000分', unlocked: this.checkAchievement('score_1000') },
      { id: 'score_10000', name: '财富自由', desc: '累计得分达到10000分', unlocked: this.checkAchievement('score_10000') },
      { id: 'rich_old_man', name: '人生巅峰', desc: '成功合成富老炮', unlocked: this.checkAchievement('rich_old_man') },
      { id: 'merge_100', name: '合成大师', desc: '完成100次合成', unlocked: this.checkAchievement('merge_100') },
      { id: 'time_5min', name: '时间管理', desc: '单局游戏时间超过5分钟', unlocked: this.checkAchievement('time_5min') },
      { id: 'speed_1000', name: '速战速决', desc: '3分钟内达到1000分', unlocked: this.checkAchievement('speed_1000') }
    ]

    let currentY = contentY + 30 - this.modalScrollY // 增加顶部间距
    const cardHeight = 80 // 减小卡片高度
    const cardSpacing = 15 // 减小间距
    const cardWidth = contentWidth - 30 // 增加可用宽度

    // 绘制标题
    this.pixelEffects.drawPixelText(
      '🎯 游戏成就',
      contentX + contentWidth / 2, currentY + 15,
      Y2KUIConfig.FONTS.PIXEL_BUTTON,
      Y2KUIConfig.COLORS.LIQUID_ORANGE,
      null,
      true, // 玻璃风格
      false
    )
    currentY += 50

    // 绘制成就卡片
    for (let achievement of achievements) {
      if (currentY + cardHeight < contentY) {
        currentY += cardHeight + cardSpacing
        continue
      }

      if (currentY > contentY + contentHeight) {
        break
      }

      // 绘制卡片背景
      if (achievement.unlocked) {
        ctx.fillStyle = 'rgba(0, 255, 65, 0.15)'
        ctx.strokeStyle = Y2KUIConfig.COLORS.MATRIX_GREEN
      } else {
        ctx.fillStyle = 'rgba(149, 165, 166, 0.15)'
        ctx.strokeStyle = Y2KUIConfig.COLORS.METAL_SILVER
      }

      ctx.fillRect(contentX + 15, currentY, cardWidth, cardHeight)
      ctx.lineWidth = 1
      ctx.strokeRect(contentX + 15, currentY, cardWidth, cardHeight)

      // 绘制成就图标（左侧）
      const iconX = contentX + 35
      const iconY = currentY + 25
      this.pixelEffects.drawPixelText(
        achievement.unlocked ? '🏆' : '🔒',
        iconX, iconY,
        { ...Y2KUIConfig.FONTS.SYSTEM_HINT, size: 20 },
        achievement.unlocked ? Y2KUIConfig.COLORS.MATRIX_GREEN : Y2KUIConfig.COLORS.LIQUID_ORANGE,
        null,
        true, // 玻璃风格
        false
      )

      // 绘制成就名称（中间左侧）
      const nameX = iconX + 50
      const nameY = currentY + 20
      this.pixelEffects.drawPixelText(
        achievement.name,
        nameX, nameY,
        { ...Y2KUIConfig.FONTS.SYSTEM_HINT, size: 14 },
        achievement.unlocked ? Y2KUIConfig.COLORS.MATRIX_GREEN : Y2KUIConfig.COLORS.DIGITAL_WHITE,
        null,
        true, // 玻璃风格
        false
      )

      // 绘制成就描述（中间下方）
      const descX = nameX
      const descY = currentY + 45
      this.pixelEffects.drawPixelText(
        achievement.desc,
        descX, descY,
        { ...Y2KUIConfig.FONTS.SYSTEM_HINT, size: 11 },
        achievement.unlocked ? Y2KUIConfig.COLORS.CYBER_CYAN : Y2KUIConfig.COLORS.METAL_SILVER,
        null,
        true, // 玻璃风格
        false
      )

      // 绘制解锁状态（右侧）
      const statusX = contentX + cardWidth - 20
      const statusY = currentY + 35
      this.pixelEffects.drawPixelText(
        achievement.unlocked ? '已解锁' : '未解锁',
        statusX, statusY,
        { ...Y2KUIConfig.FONTS.SYSTEM_HINT, size: 10 },
        achievement.unlocked ? Y2KUIConfig.COLORS.MATRIX_GREEN : Y2KUIConfig.COLORS.METAL_SILVER,
        null,
        true, // 玻璃风格
        false
      )

      currentY += cardHeight + cardSpacing
    }
  }
  
  /**
   * 检查成就解锁状态
   */
  checkAchievement(achievementId) {
    // 这里可以根据游戏数据检查成就解锁状态
    // 暂时返回一些模拟数据
    const mockUnlocked = {
      'first_merge': true,
      'score_100': this.main.gameData.totalScore >= 100,
      'score_1000': this.main.gameData.totalScore >= 1000,
      'score_10000': this.main.gameData.totalScore >= 10000,
      'rich_old_man': false,
      'merge_100': false,
      'time_5min': false,
      'speed_1000': false
    }
    return mockUnlocked[achievementId] || false
  }
  

  
  /**
   * 执行分享操作
   */
  shareToWeChat(shareType) {
    const shareData = {
      title: '《21世纪进化论》- 物理合成小游戏',
      desc: '简单易上手的合成玩法，12个等级角色等你解锁！快来挑战吧！',
      path: '/pages/index/index',
      imageUrl: 'game_media/images/11.png'
    }
    
    // 直接分享给微信好友
    if (wx.shareAppMessage) {
      wx.shareAppMessage(shareData)
    }
    
    // 显示分享成功提示
    this.showShareSuccess()
  }
  
  /**
   * 显示分享成功提示
   */
  showShareSuccess() {
    if (wx.showToast) {
      wx.showToast({
        title: '分享成功！',
        icon: 'success',
        duration: 2000
      })
    }
  }
  

  
  update() {
    // 更新动画时间
    this.animationTime += 16.67 // 假设60fps

    // 更新Y2K效果系统
    this.pixelEffects.update(16.67)
    this.cyberDecorations.update(16.67)

    // 更新按钮动画
    this.updateButtonAnimations()
  }
  
  /**
   * 更新粒子动画
   */
  updateParticles() {
    for (let particle of this.particles) {
      particle.x += particle.vx
      particle.y += particle.vy
      
      // 边界反弹
      if (particle.x <= 0 || particle.x >= this.screenWidth) {
        particle.vx *= -1
      }
      if (particle.y <= 0 || particle.y >= this.screenHeight) {
        particle.vy *= -1
      }
      
      // 保持粒子在屏幕内
      particle.x = Math.max(0, Math.min(this.screenWidth, particle.x))
      particle.y = Math.max(0, Math.min(this.screenHeight, particle.y))
    }
  }
  
  /**
   * 更新按钮动画
   */
  updateButtonAnimations() {
    for (let button of this.buttons) {
      if (button.isMain) {
        button.pulseAnimation = Math.sin(this.animationTime * 0.003) * 0.1 + 1
      } else {
        button.pulseAnimation = 1
      }

      // 移除随机故障效果
    }

    // 更新标题故障效果
    this.titleGlitchTimer += 16
    if (this.titleGlitchTimer > 3000) {
      this.titleGlitchTimer = 0
    }
  }

  async render(ctx) {
    // 应用像素化设置
    this.pixelEffects.enablePixelation()

    // 绘制Y2K风格背景
    this.renderY2KBackground(ctx)

    // 绘制赛博朋克装饰
    this.cyberDecorations.render()

    // 绘制标题
    this.renderY2KTitle(ctx)

    // 绘制按钮
    this.renderY2KButtons(ctx)

    // 绘制像素效果
    this.pixelEffects.render()

    // 绘制弹窗
    if (this.showModal) {
      this.renderY2KModal(ctx)
    }
  }
  
  /**
   * 绘制Y2K风格背景
   */
  renderY2KBackground(ctx) {
    // 基础黑色背景
    ctx.fillStyle = Y2KUIConfig.COLORS.PIXEL_BLACK
    ctx.fillRect(0, 0, this.screenWidth, this.screenHeight)

    // 绘制简洁的动态网格
    this.drawDigitalGrid(ctx)
  }

  /**
   * 绘制数字网格
   */
  drawDigitalGrid(ctx) {
    ctx.strokeStyle = Y2KUIConfig.COLORS.ALPHA.SCAN_LINE
    ctx.lineWidth = 1

    const gridSize = 60 // 增大网格尺寸，减少线条密度
    this.backgroundAnimOffset += 0.2 // 减慢动画速度

    // 只绘制部分网格线，创建更简洁的效果
    const offset = this.backgroundAnimOffset % gridSize

    // 垂直线（减少数量）
    for (let x = offset; x < this.screenWidth; x += gridSize * 2) {
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, this.screenHeight)
      ctx.stroke()
    }

    // 水平线（减少数量）
    for (let y = offset; y < this.screenHeight; y += gridSize * 2) {
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(this.screenWidth, y)
      ctx.stroke()
    }
  }
  
  /**
   * 绘制Y2K风格标题
   */
  renderY2KTitle(ctx) {
    const titleY = this.screenHeight * 0.25
    const titleText = '21世纪进化论'

    // 移除装饰框架和边框

    // 移除故障效果，只使用正常文字渲染
    this.pixelEffects.drawPixelText(
      titleText,
      this.screenWidth / 2, titleY,
      Y2KUIConfig.FONTS.PIXEL_TITLE,
      Y2KUIConfig.COLORS.CYBER_CYAN,
      Y2KUIConfig.COLORS.PIXEL_BLACK
    )

    // 绘制副标题
    this.pixelEffects.drawPixelText(
      '< SYSTEM INITIALIZED >',
      this.screenWidth / 2, titleY + 60,
      Y2KUIConfig.FONTS.SYSTEM_HINT,
      Y2KUIConfig.COLORS.MATRIX_GREEN,
      Y2KUIConfig.COLORS.PIXEL_BLACK
    )

    // 绘制游戏统计信息
    this.renderY2KGameStats(ctx)
  }
  
  /**
   * 渲染Y2K风格游戏统计信息
   */
  renderY2KGameStats(ctx) {
    const centerX = this.screenWidth / 2
    let currentY = this.screenHeight * 0.25 + 100

    // 获取统计数据
    const highScore = this.main.gameData.highScore
    const gameCount = this.main.gameData.gameCount
    const highScoreTime = this.main.gameData.highScoreTime

    // 格式化时间显示
    const formatTime = (seconds) => {
      if (seconds === 0) return 'NULL'
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    // 绘制统计信息面板
    const panelWidth = 300
    const panelHeight = 120
    const panelX = centerX - panelWidth / 2
    const panelY = currentY - 20

    // 面板背景（半透明）
    ctx.fillStyle = Y2KUIConfig.COLORS.ALPHA.BACKGROUND_OVERLAY
    ctx.fillRect(panelX, panelY, panelWidth, panelHeight)

    // 绘制统计信息
    const stats = [
      { label: 'HIGH_SCORE', value: highScore > 0 ? highScore.toString() : 'NULL', color: Y2KUIConfig.COLORS.CYBER_CYAN },
      { label: 'GAME_COUNT', value: gameCount.toString(), color: Y2KUIConfig.COLORS.MATRIX_GREEN },
      { label: 'BEST_TIME', value: formatTime(highScoreTime), color: Y2KUIConfig.COLORS.ELECTRIC_PURPLE }
    ]

    stats.forEach((stat, index) => {
      const statY = currentY + index * 25

      // 绘制标签
      this.pixelEffects.drawPixelText(
        stat.label + ':',
        centerX - 60, statY,
        Y2KUIConfig.FONTS.SYSTEM_HINT,
        Y2KUIConfig.COLORS.DIGITAL_WHITE,
        Y2KUIConfig.COLORS.PIXEL_BLACK
      )

      // 绘制数值
      this.pixelEffects.drawPixelText(
        stat.value,
        centerX + 60, statY,
        Y2KUIConfig.FONTS.SYSTEM_HINT,
        stat.color,
        Y2KUIConfig.COLORS.PIXEL_BLACK
      )
    })
  }
  
  /**
   * 绘制Y2K风格按钮
   */
  renderY2KButtons(ctx) {
    this.buttons.forEach((button, index) => {
      const isHovered = this.hoveredButton === index
      button.isHovered = isHovered

      // 计算按钮缩放
      const scale = button.pulseAnimation
      const scaledWidth = button.width * scale
      const scaledHeight = button.height * scale

      // 按钮配置
      const buttonConfig = {
        color: button.color,
        gradient: button.gradient,
        borderColor: button.borderColor,
        isHovered: isHovered,
        glitchEffect: button.glitchEffect,
        glassStyle: true // 启用玻璃拟态风格
      }

      // 绘制按钮
      this.pixelEffects.drawPixelButton(
        button.x, button.y,
        scaledWidth, scaledHeight,
        buttonConfig
      )

      // 绘制按钮文字
      const fontConfig = button.isMain ?
        Y2KUIConfig.FONTS.PIXEL_BUTTON :
        { ...Y2KUIConfig.FONTS.PIXEL_BUTTON, size: 20 }

      // 移除故障效果，只使用正常文字渲染
      this.pixelEffects.drawPixelText(
        button.text,
        button.x, button.y,
        fontConfig,
        'rgba(255, 255, 255, 0.9)', // 玻璃风格文字颜色
        null, // 不需要描边
        true, // 启用玻璃风格
        isHovered // 传递悬停状态
      )

      // 主按钮特殊装饰
      if (button.isMain && isHovered) {
        this.cyberDecorations.drawDecorativeFrame(
          button.x - scaledWidth / 2 - 10,
          button.y - scaledHeight / 2 - 10,
          scaledWidth + 20,
          scaledHeight + 20
        )
      }
    })

    // 绘制音频按钮
    this.renderY2KAudioButton(ctx)
  }

  /**
   * 绘制Y2K风格音频按钮
   */
  renderY2KAudioButton(ctx) {
    const button = this.audioButton
    const isMuted = this.main.audioManager.isMuted

    // 按钮配置
    const buttonConfig = {
      color: isMuted ? button.mutedColor : button.color,
      borderColor: button.borderColor,
      isHovered: button.isHovered,
      glassStyle: true // 启用玻璃拟态风格
    }

    // 绘制按钮
    this.pixelEffects.drawPixelButton(
      button.x, button.y,
      button.width, button.height,
      buttonConfig
    )

    // 绘制音频图标（使用现代符号）
    const iconSymbol = isMuted ? '🔇' : '🔊'
    this.pixelEffects.drawPixelText(
      iconSymbol,
      button.x, button.y,
      Y2KUIConfig.FONTS.PIXEL_BUTTON,
      'rgba(255, 255, 255, 0.9)', // 玻璃风格文字颜色
      null, // 不需要描边
      true, // 启用玻璃风格
      button.isHovered // 传递悬停状态
    )
  }

  /**
   * 绘制Y2K风格弹窗
   */
  renderY2KModal(ctx) {
    // 绘制半透明背景
    ctx.fillStyle = Y2KUIConfig.COLORS.ALPHA.BACKGROUND_OVERLAY
    ctx.fillRect(0, 0, this.screenWidth, this.screenHeight)

    // 弹窗尺寸和位置 - 响应式尺寸
    const modalWidth = Math.min(Y2KUIConfig.DIMENSIONS.MODAL_PANEL.width, this.screenWidth * 0.9)
    const modalHeight = Math.min(Y2KUIConfig.DIMENSIONS.MODAL_PANEL.height, this.screenHeight * 0.8)
    const modalX = (this.screenWidth - modalWidth) / 2
    const modalY = (this.screenHeight - modalHeight) / 2

    // 绘制弹窗背景
    ctx.fillStyle = Y2KUIConfig.COLORS.PIXEL_BLACK
    ctx.fillRect(modalX, modalY, modalWidth, modalHeight)

    // 绘制像素化边框
    this.pixelEffects.drawPixelBorder(
      modalX, modalY, modalWidth, modalHeight,
      Y2KUIConfig.COLORS.CYBER_CYAN,
      Y2KUIConfig.DIMENSIONS.PIXEL_BORDER
    )

    // 移除装饰框架

    // 绘制标题 - 使用更小的字体
    const titleFont = {
      ...Y2KUIConfig.FONTS.PIXEL_TITLE,
      size: Math.min(28, this.screenWidth * 0.06) // 响应式标题字体大小
    }
    this.pixelEffects.drawPixelText(
      this.modalTitle,
      this.screenWidth / 2, modalY + 35,
      titleFont,
      Y2KUIConfig.COLORS.CYBER_CYAN,
      null,
      true, // 玻璃风格
      false
    )

    // 绘制内容区域 - 调整布局给标题更多空间
    const contentX = modalX + 15  // 减少左边距
    const contentY = modalY + 75
    const contentWidth = modalWidth - 30  // 减少总边距
    const contentHeight = modalHeight - 140  // 减少底部预留空间，给内容更多高度

    // 设置内容区域裁剪 - 给左侧更多空间
    ctx.save()
    ctx.beginPath()
    ctx.rect(contentX - 5, contentY, contentWidth + 10, contentHeight)
    ctx.clip()

    // 根据弹窗类型绘制不同内容
    if (this.modalType === 'achievements') {
      this.renderAchievementsContent(ctx, contentX, contentY, contentWidth, contentHeight)
    } else {
      this.renderRulesContent(ctx, contentX, contentY, contentWidth, contentHeight)
    }

    ctx.restore()

    // 绘制关闭按钮
    const buttonY = modalY + modalHeight - 60
    const buttonConfig = {
      color: Y2KUIConfig.COLORS.NEON_PINK,
      borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
      isHovered: false,
      glassStyle: true // 启用玻璃拟态风格
    }

    this.pixelEffects.drawPixelButton(
      this.screenWidth / 2, buttonY,
      200, 50,
      buttonConfig
    )

    this.pixelEffects.drawPixelText(
      this.modalButtonText,
      this.screenWidth / 2, buttonY,
      Y2KUIConfig.FONTS.PIXEL_BUTTON,
      'rgba(255, 255, 255, 0.9)', // 玻璃风格文字颜色
      null, // 不需要描边
      true, // 启用玻璃风格
      false // 不是悬停状态
    )
  }

  /**
   * 渲染规则内容
   */
  renderRulesContent(ctx, contentX, contentY, contentWidth, contentHeight) {
    let currentY = contentY + 10 - this.modalScrollY // 进一步减少顶部间距
    const baseLineHeight = Math.max(18, this.screenWidth * 0.04) // 响应式行高
    const lines = this.modalContent.split('\n')
    const maxTextWidth = contentWidth - 20 // 减少边距，给文字更多空间

    for (let line of lines) {
      if (currentY > contentY + contentHeight) break
      if (currentY + baseLineHeight < contentY) {
        currentY += baseLineHeight
        continue
      }

      // 处理不同类型的文本行 - 增大字体大小
      let fontSize = Math.max(14, Math.min(16, this.screenWidth * 0.038))
      let color = Y2KUIConfig.COLORS.DIGITAL_WHITE
      let lineHeight = baseLineHeight

      if (line.startsWith('##')) {
        // 二级标题
        fontSize = Math.max(16, Math.min(18, this.screenWidth * 0.042))
        color = Y2KUIConfig.COLORS.CYBER_CYAN
        line = line.replace('##', '').trim()
        lineHeight = baseLineHeight * 1.2
      } else if (line.startsWith('#')) {
        // 一级标题
        fontSize = Math.max(18, Math.min(20, this.screenWidth * 0.046))
        color = Y2KUIConfig.COLORS.LIQUID_ORANGE
        line = line.replace('#', '').trim()
        lineHeight = baseLineHeight * 1.3
      } else if (line.startsWith('-')) {
        // 列表项
        fontSize = Math.max(13, Math.min(15, this.screenWidth * 0.036))
        color = Y2KUIConfig.COLORS.MATRIX_GREEN
      }

      if (line.trim()) {
        // 检查是否是角色显示部分
        if (line.includes('CHAR_DISPLAY_SECTION_')) {
          currentY = this.renderCharacterSection(ctx, line, contentX, currentY, contentWidth, contentHeight, contentY, baseLineHeight)
        } else {
          // 设置字体用于测量
          ctx.font = `${fontSize}px monospace`

          // 文字换行处理
          const wrappedLines = this.wrapText(ctx, line, maxTextWidth)

          for (let wrappedLine of wrappedLines) {
            if (currentY > contentY + contentHeight) break
            if (currentY + lineHeight >= contentY) {
              // 直接使用canvas绘制左对齐文字，而不是使用pixelEffects
              ctx.font = `${fontSize}px monospace`
              ctx.textAlign = 'left'
              ctx.textBaseline = 'top'

              // 绘制文字阴影
              ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
              ctx.fillText(wrappedLine, contentX + 22, currentY + 1)

              // 绘制主文字
              ctx.fillStyle = color
              ctx.fillText(wrappedLine, contentX + 20, currentY)
            }
            currentY += lineHeight
          }
        }
      } else {
        // 空行
        currentY += lineHeight * 0.5
      }
    }
  }

  /**
   * 渲染角色展示部分
   */
  renderCharacterSection(ctx, sectionLine, contentX, currentY, contentWidth, contentHeight, contentY, lineHeight) {
    const CHARACTER_CONFIG = [
      { level: 1, name: '小宝', color: '#FF6B6B', radius: 15, score: 1, isInitial: true, imagePath: 'game_media/images/char_1.png' },
      { level: 2, name: '年轻哥', color: '#FF8E8E', radius: 20, score: 2, isInitial: true, imagePath: 'game_media/images/char_2.png' },
      { level: 3, name: '走读生', color: '#9B59B6', radius: 25, score: 4, isInitial: true, imagePath: 'game_media/images/char_3.png' },
      { level: 4, name: '大满贯', color: '#F39C12', radius: 30, score: 6, isInitial: false, imagePath: 'game_media/images/char_4.png' },
      { level: 5, name: '研不休', color: '#F1C40F', radius: 35, score: 8, isInitial: false, imagePath: 'game_media/images/char_5.png' },
      { level: 6, name: '博学者', color: '#27AE60', radius: 40, score: 10, isInitial: false, imagePath: 'game_media/images/char_6.png' },
      { level: 7, name: '企小工', color: '#E67E22', radius: 45, score: 100, isInitial: false, imagePath: 'game_media/images/char_7.png' },
      { level: 8, name: '创老板', color: '#8B4513', radius: 50, score: 1000, isInitial: false, imagePath: 'game_media/images/char_8.png' },
      { level: 9, name: '编内人', color: '#E74C3C', radius: 55, score: 1500, isInitial: false, imagePath: 'game_media/images/char_9.png' },
      { level: 10, name: '富小哥', color: '#C0392B', radius: 60, score: 2000, isInitial: false, imagePath: 'game_media/images/char_10.png' },
      { level: 11, name: '富老炮', color: '#A93226', radius: 65, score: 100000000, isInitial: false, isUltimate: true, imagePath: 'game_media/images/char_11.png' },
      { level: 12, name: '丐帮主', color: '#2C3E50', radius: 70, score: 0, isInitial: false, isSpecial: true, imagePath: 'game_media/images/char_12.png' }
    ]

    let characters = []
    if (sectionLine.includes('BASIC')) {
      characters = CHARACTER_CONFIG.filter(char => char.isInitial)
    } else if (sectionLine.includes('ADVANCED')) {
      characters = CHARACTER_CONFIG.filter(char => !char.isInitial && !char.isUltimate && !char.isSpecial)
    } else if (sectionLine.includes('SPECIAL')) {
      characters = CHARACTER_CONFIG.filter(char => char.isUltimate || char.isSpecial)
    }

    const charSize = 35 // 增大角色头像大小
    const charSpacing = lineHeight + 15 // 增加角色间距

    for (let char of characters) {
      if (currentY > contentY + contentHeight) break

      // 尝试绘制角色头像图片
      const image = this.main.resourceLoader.getLoadedImage(char.imagePath)
      if (image && image.complete) {
        // 绘制圆形裁剪的角色头像
        ctx.save()
        ctx.beginPath()
        ctx.arc(contentX + 40, currentY + charSize/2, charSize/2, 0, Math.PI * 2)
        ctx.clip()
        ctx.drawImage(image, contentX + 40 - charSize/2, currentY, charSize, charSize)
        ctx.restore()

        // 绘制头像边框
        ctx.beginPath()
        ctx.arc(contentX + 40, currentY + charSize/2, charSize/2, 0, Math.PI * 2)
        ctx.strokeStyle = Y2KUIConfig.COLORS.DIGITAL_WHITE
        ctx.lineWidth = 2
        ctx.stroke()
      } else {
        // 如果图片未加载，使用颜色圆形作为备用
        ctx.save()
        ctx.beginPath()
        ctx.arc(contentX + 40, currentY + charSize/2, charSize/2, 0, Math.PI * 2)
        ctx.fillStyle = char.color
        ctx.fill()
        ctx.strokeStyle = Y2KUIConfig.COLORS.DIGITAL_WHITE
        ctx.lineWidth = 2
        ctx.stroke()
        ctx.restore()
      }

      // 绘制角色名字 - 增大字体
      ctx.font = '16px monospace'
      ctx.textAlign = 'left'
      ctx.textBaseline = 'middle'
      ctx.fillStyle = Y2KUIConfig.COLORS.DIGITAL_WHITE
      ctx.fillText(char.name, contentX + 90, currentY + charSize/2 - 6)

      // 绘制分数 - 增大字体
      ctx.font = '14px monospace'
      ctx.fillStyle = Y2KUIConfig.COLORS.MATRIX_GREEN
      const scoreText = char.score === 100000000 ? '+100000000' : `+${char.score}`
      ctx.fillText(`分数：${scoreText}`, contentX + 90, currentY + charSize/2 + 10)

      currentY += charSpacing
    }

    return currentY
  }

}