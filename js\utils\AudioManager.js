export class AudioManager {
  constructor() {
    // 音频资源管理
    this.sounds = {} // 音效缓存
    this.bgm = null // 当前背景音乐
    this.currentBGM = null // 当前BGM类型
    
    // 音频状态控制
    this.isMuted = false // 全局静音开关
    this.bgmVolume = 0.3 // 背景音乐默认音量
    this.sfxVolume = 0.6 // 音效默认音量
    
    // 音频配置
    this.audioConfig = {
      // 背景音乐配置
      bgm: {
        menu: { path: 'game_media/audio/menu.mp3', volume: 0.3, loop: true },
        gameplay: { path: 'game_media/audio/gameplay.mp3', volume: 0.3, loop: true },
        win: { path: 'game_media/audio/win.mp3', volume: 0.4, loop: true },
        lose: { path: 'game_media/audio/lose.mp3', volume: 0.4, loop: true }
      },
      // 音效配置
      sfx: {
        click: { path: 'game_media/audio/click.mp3', volume: 0.6, loop: false },
        drop: { path: 'game_media/audio/drop.mp3', volume: 0.6, loop: false },
        synthesis: { path: 'game_media/audio/synthesis.mp3', volume: 0.6, loop: false },
        transform: { path: 'game_media/audio/transform.mp3', volume: 0.6, loop: false }
      }
    }
    
    this.init()
  }
  
  /**
   * 初始化音频系统
   */
  init() {
    this.initAudioContext()
    this.preloadSounds()
  }
  
  /**
   * 初始化音频上下文
   */
  initAudioContext() {
    if (wx.createInnerAudioContext) {
      this.audioContext = wx.createInnerAudioContext()
    }
  }
  
  /**
   * 预加载所有音频资源
   */
  preloadSounds() {
    // 预加载音效
    Object.keys(this.audioConfig.sfx).forEach(name => {
      const config = this.audioConfig.sfx[name]
      this.loadSound(name, config.path, config.volume)
    })
  }
  
  /**
   * 加载音效
   */
  loadSound(name, path, volume = 0.6) {
    try {
      const audio = wx.createInnerAudioContext()
      audio.src = path
      audio.volume = this.isMuted ? 0 : volume * this.sfxVolume
      audio.loop = false
      
      // 监听播放完成事件，自动清理
      audio.onEnded(() => {
        audio.destroy()
      })
      
      this.sounds[name] = audio
    } catch (error) {
      console.warn(`Failed to load sound: ${name}`, error)
    }
  }
  
  /**
   * 播放音效
   */
  playSound(name) {
    if (this.isMuted) return
    
    const sound = this.sounds[name]
    if (sound) {
      try {
        // 创建新的音频实例，避免重复播放冲突
        const newSound = wx.createInnerAudioContext()
        newSound.src = sound.src
        newSound.volume = sound.volume
        newSound.loop = false
        
        newSound.onEnded(() => {
          newSound.destroy()
        })
        
        newSound.play()
      } catch (error) {
        console.warn(`Failed to play sound: ${name}`, error)
      }
    }
  }
  
  /**
   * 播放背景音乐
   */
  playBGM(type) {
    if (this.isMuted) return
    
    const config = this.audioConfig.bgm[type]
    if (!config) {
      console.warn(`BGM type not found: ${type}`)
      return
    }
    
    // 如果当前BGM相同，不重复播放
    if (this.currentBGM === type && this.bgm) {
      return
    }
    
    try {
      // 停止当前BGM
      this.stopBGM()
      
      // 创建新的BGM
      this.bgm = wx.createInnerAudioContext()
      this.bgm.src = config.path
      this.bgm.volume = this.isMuted ? 0 : config.volume * this.bgmVolume
      this.bgm.loop = config.loop
      
      this.bgm.onEnded(() => {
        // BGM循环播放，不需要销毁
        if (this.bgm && this.bgm.loop) {
          this.bgm.play()
        }
      })
      
      this.currentBGM = type
      this.bgm.play()
    } catch (error) {
      console.warn(`Failed to play BGM: ${type}`, error)
    }
  }
  
  /**
   * 停止背景音乐
   */
  stopBGM() {
    if (this.bgm) {
      this.bgm.stop()
      this.bgm.destroy()
      this.bgm = null
      this.currentBGM = null
    }
  }
  
  /**
   * 暂停背景音乐
   */
  pauseBGM() {
    if (this.bgm) {
      this.bgm.pause()
    }
  }
  
  /**
   * 恢复背景音乐
   */
  resumeBGM() {
    if (this.bgm && !this.isMuted) {
      this.bgm.play()
    }
  }
  
  /**
   * 设置背景音乐音量
   */
  setBGMVolume(volume) {
    this.bgmVolume = Math.max(0, Math.min(1, volume))
    
    if (this.bgm) {
      const config = this.audioConfig.bgm[this.currentBGM]
      if (config) {
        this.bgm.volume = this.isMuted ? 0 : config.volume * this.bgmVolume
      }
    }
  }
  
  /**
   * 设置音效音量
   */
  setSFXVolume(volume) {
    this.sfxVolume = Math.max(0, Math.min(1, volume))
    
    // 更新所有音效音量
    Object.keys(this.sounds).forEach(name => {
      const sound = this.sounds[name]
      const config = this.audioConfig.sfx[name]
      if (sound && config) {
        sound.volume = this.isMuted ? 0 : config.volume * this.sfxVolume
      }
    })
  }
  
  /**
   * 全局静音
   */
  mute() {
    this.isMuted = true
    
    // 静音BGM
    if (this.bgm) {
      this.bgm.volume = 0
    }
    
    // 静音所有音效
    Object.values(this.sounds).forEach(sound => {
      sound.volume = 0
    })
  }
  
  /**
   * 取消静音
   */
  unmute() {
    this.isMuted = false
    
    // 恢复BGM音量
    if (this.bgm) {
      const config = this.audioConfig.bgm[this.currentBGM]
      if (config) {
        this.bgm.volume = config.volume * this.bgmVolume
      }
    }
    
    // 恢复所有音效音量
    Object.keys(this.sounds).forEach(name => {
      const sound = this.sounds[name]
      const config = this.audioConfig.sfx[name]
      if (sound && config) {
        sound.volume = config.volume * this.sfxVolume
      }
    })
  }
  
  /**
   * 切换静音状态
   */
  toggleMute() {
    if (this.isMuted) {
      this.unmute()
    } else {
      this.mute()
    }
  }
  
  /**
   * 根据游戏状态自动切换BGM
   */
  switchBGMByGameState(gameState) {
    switch (gameState) {
      case 'menu':
        this.playBGM('menu')
        break
      case 'gameplay':
        this.playBGM('gameplay')
        break
      case 'win':
        this.playBGM('win')
        break
      case 'lose':
        this.playBGM('lose')
        break
      default:
        console.warn(`Unknown game state: ${gameState}`)
    }
  }
  
  // ========== 游戏音效方法 ==========
  
  /**
   * 播放点击音效
   */
  playClickSound() {
    this.playSound('click')
  }
  
  /**
   * 播放投放音效
   */
  playDropSound() {
    this.playSound('drop')
  }
  
  /**
   * 播放合成音效
   */
  playSynthesisSound() {
    this.playSound('synthesis')
  }
  
  /**
   * 播放转换音效
   */
  playTransformSound() {
    this.playSound('transform')
  }
  
  /**
   * 清理音频资源
   */
  destroy() {
    // 停止并清理所有音效
    Object.values(this.sounds).forEach(sound => {
      sound.stop()
      sound.destroy()
    })
    
    // 停止并清理BGM
    if (this.bgm) {
      this.bgm.stop()
      this.bgm.destroy()
    }
    
    // 重置状态
    this.sounds = {}
    this.bgm = null
    this.currentBGM = null
  }
} 