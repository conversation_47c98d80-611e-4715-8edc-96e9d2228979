import { GameConfig } from '../config/GameConfig.js'

/**
 * 难度管理器 - 实现羊了个羊式的高难度设计
 * 目标：让大部分玩家在富二代阶段(10级)失败，通关率控制在5%以下
 */
export class DifficultyManager {
  constructor(gameData) {
    this.gameData = gameData
    this.reset()
  }
  
  reset() {
    // 当前难度阶段
    this.currentPhase = 'TUTORIAL'
    this.phaseStartTime = Date.now()
    
    // 动态难度参数
    this.dynamicGravityMultiplier = 1.0
    this.dynamicFrictionMultiplier = 1.0
    this.dynamicInstabilityFactor = 1.0
    
    // 时间压力状态
    this.timePressureActive = false
    this.timePressureStartTime = 0
    
    // 混乱模式状态
    this.chaosMode = false
    this.gravitySpike = {
      active: false,
      startTime: 0,
      multiplier: 1.0
    }
    
    // 恶意生成状态
    this.maliciousGeneration = {
      badStreak: {
        active: false,
        count: 0,
        target: 0
      },
      antiStrategy: {
        playerPattern: [],
        lastPositions: []
      }
    }
    
    // 游戏统计（用于自适应难度）
    this.gameStats = {
      recentGames: [],
      currentGameStartTime: Date.now()
    }
  }
  
  /**
   * 更新难度系统
   */
  update(deltaTime) {
    const currentTime = Date.now()
    const gameTime = currentTime - this.gameStats.currentGameStartTime
    
    // 更新游戏阶段
    this.updateGamePhase()
    
    // 更新时间压力
    this.updateTimePressure(gameTime)
    
    // 更新混乱模式
    if (this.chaosMode) {
      this.updateChaosMode(deltaTime)
    }
    
    // 更新动态难度参数
    this.updateDynamicDifficulty()
  }
  
  /**
   * 更新游戏阶段
   */
  updateGamePhase() {
    const score = this.gameData.score
    const phases = GameConfig.DIFFICULTY.SHEEP_DIFFICULTY.PHASES
    
    let newPhase = this.currentPhase
    
    if (score >= phases.NIGHTMARE.scoreRange[0]) {
      newPhase = 'NIGHTMARE'
      this.chaosMode = true
    } else if (score >= phases.CHALLENGING.scoreRange[0]) {
      newPhase = 'CHALLENGING'
    } else if (score >= phases.NORMAL.scoreRange[0]) {
      newPhase = 'NORMAL'
    } else {
      newPhase = 'TUTORIAL'
    }
    
    if (newPhase !== this.currentPhase) {
      this.currentPhase = newPhase
      this.phaseStartTime = Date.now()
      console.log(`🎯 难度阶段切换到: ${newPhase}`)
    }
  }
  
  /**
   * 更新时间压力
   */
  updateTimePressure(gameTime) {
    const timePressureConfig = GameConfig.DIFFICULTY.TIME_PRESSURE
    
    if (!this.timePressureActive && gameTime >= timePressureConfig.START_TIME) {
      this.timePressureActive = true
      this.timePressureStartTime = Date.now()
      console.log('⏰ 时间压力模式激活')
    }
    
    if (this.timePressureActive) {
      const pressureTime = (Date.now() - this.timePressureStartTime) / 1000
      
      // 逐渐增加重力
      const gravityIncrease = pressureTime * timePressureConfig.GRAVITY_INCREASE_RATE
      this.dynamicGravityMultiplier = Math.min(
        1.0 + gravityIncrease,
        timePressureConfig.MAX_GRAVITY_MULTIPLIER
      )
      
      // 逐渐减少摩擦力
      const frictionDecrease = pressureTime * timePressureConfig.FRICTION_DECREASE_RATE
      this.dynamicFrictionMultiplier = Math.max(
        1.0 - frictionDecrease,
        timePressureConfig.MIN_FRICTION_MULTIPLIER
      )
    }
  }
  
  /**
   * 更新混乱模式
   */
  updateChaosMode(deltaTime) {
    const chaosConfig = GameConfig.DIFFICULTY.SHEEP_DIFFICULTY.CHAOS_MODE
    
    // 随机重力突增
    if (chaosConfig.RANDOM_GRAVITY_SPIKES && !this.gravitySpike.active) {
      if (Math.random() < chaosConfig.SPIKE_PROBABILITY * (deltaTime / 16.67)) {
        this.gravitySpike.active = true
        this.gravitySpike.startTime = Date.now()
        this.gravitySpike.multiplier = chaosConfig.SPIKE_MULTIPLIER
        console.log('💥 重力突增触发!')
      }
    }
    
    // 重力突增持续时间检查
    if (this.gravitySpike.active) {
      const spikeTime = Date.now() - this.gravitySpike.startTime
      if (spikeTime >= chaosConfig.SPIKE_DURATION) {
        this.gravitySpike.active = false
        this.gravitySpike.multiplier = 1.0
        console.log('💥 重力突增结束')
      }
    }
  }
  
  /**
   * 更新动态难度参数
   */
  updateDynamicDifficulty() {
    const highestLevel = this.getHighestLevelInGame()
    const levelModifier = GameConfig.DIFFICULTY.LEVEL_MODIFIERS[highestLevel] || 
                         GameConfig.DIFFICULTY.LEVEL_MODIFIERS[1]
    
    const phaseMultiplier = GameConfig.DIFFICULTY.SHEEP_DIFFICULTY.PHASES[this.currentPhase]?.difficultyMultiplier || 1.0
    
    // 计算最终难度参数
    this.dynamicGravityMultiplier *= levelModifier.gravityMultiplier * phaseMultiplier
    this.dynamicFrictionMultiplier *= levelModifier.frictionMultiplier
    this.dynamicInstabilityFactor = levelModifier.instabilityFactor * phaseMultiplier
    
    // 应用重力突增
    if (this.gravitySpike.active) {
      this.dynamicGravityMultiplier *= this.gravitySpike.multiplier
    }
  }
  
  /**
   * 获取当前重力倍数
   */
  getGravityMultiplier() {
    return this.dynamicGravityMultiplier
  }
  
  /**
   * 获取当前摩擦力倍数
   */
  getFrictionMultiplier() {
    return this.dynamicFrictionMultiplier
  }
  
  /**
   * 获取当前不稳定因子
   */
  getInstabilityFactor() {
    return this.dynamicInstabilityFactor
  }
  
  /**
   * 获取游戏中最高等级
   */
  getHighestLevelInGame() {
    if (!this.gameData.fruits || this.gameData.fruits.length === 0) {
      return 1
    }
    
    return Math.max(...this.gameData.fruits.map(fruit => fruit.level))
  }
  
  /**
   * 获取当前阶段的生成权重
   */
  getGenerationWeights() {
    const weights = GameConfig.FRUITS.GENERATION_WEIGHTS[this.currentPhase] || 
                   GameConfig.FRUITS.GENERATION_WEIGHTS.NORMAL
    
    // 应用恶意生成调整
    return this.applyMaliciousGeneration(weights)
  }
  
  /**
   * 应用恶意生成机制
   */
  applyMaliciousGeneration(baseWeights) {
    const maliciousConfig = GameConfig.FRUITS.MALICIOUS_GENERATION
    
    if (!maliciousConfig.ENABLED) {
      return baseWeights
    }
    
    let adjustedWeights = { ...baseWeights }
    
    // 检查是否触发恶意连击
    if (this.shouldTriggerBadStreak()) {
      adjustedWeights = this.applyBadStreak(adjustedWeights)
    }
    
    // 检查空间压迫
    if (this.shouldApplySpacePressure()) {
      adjustedWeights = this.applySpacePressure(adjustedWeights)
    }
    
    return adjustedWeights
  }
  
  /**
   * 检查是否应该触发恶意连击
   */
  shouldTriggerBadStreak() {
    const badStreakConfig = GameConfig.FRUITS.MALICIOUS_GENERATION.BAD_STREAK
    
    if (this.maliciousGeneration.badStreak.active) {
      return this.maliciousGeneration.badStreak.count < this.maliciousGeneration.badStreak.target
    }
    
    if (Math.random() < badStreakConfig.PROBABILITY) {
      this.maliciousGeneration.badStreak.active = true
      this.maliciousGeneration.badStreak.count = 0
      this.maliciousGeneration.badStreak.target = 
        badStreakConfig.MIN_LENGTH + 
        Math.floor(Math.random() * (badStreakConfig.MAX_LENGTH - badStreakConfig.MIN_LENGTH + 1))
      
      console.log(`😈 恶意连击触发! 目标: ${this.maliciousGeneration.badStreak.target}`)
      return true
    }
    
    return false
  }
  
  /**
   * 应用恶意连击
   */
  applyBadStreak(weights) {
    const badStreakConfig = GameConfig.FRUITS.MALICIOUS_GENERATION.BAD_STREAK
    
    this.maliciousGeneration.badStreak.count++
    
    if (this.maliciousGeneration.badStreak.count >= this.maliciousGeneration.badStreak.target) {
      this.maliciousGeneration.badStreak.active = false
    }
    
    // 强制生成高级球
    const adjustedWeights = {}
    Object.keys(weights).forEach(level => {
      adjustedWeights[level] = level == badStreakConfig.BAD_BALL_LEVEL ? 100 : 0
    })
    
    return adjustedWeights
  }
  
  /**
   * 检查是否应该应用空间压迫
   */
  shouldApplySpacePressure() {
    const spacePressureConfig = GameConfig.FRUITS.MALICIOUS_GENERATION.SPACE_PRESSURE
    
    if (!spacePressureConfig.ENABLED) {
      return false
    }
    
    // 简单的空间占用率计算
    const totalFruits = this.gameData.fruits.length
    const maxFruits = 20 // 假设最大容量
    const occupancyRate = totalFruits / maxFruits
    
    return occupancyRate >= spacePressureConfig.TRIGGER_THRESHOLD
  }
  
  /**
   * 应用空间压迫
   */
  applySpacePressure(weights) {
    const spacePressureConfig = GameConfig.FRUITS.MALICIOUS_GENERATION.SPACE_PRESSURE
    
    // 增加大球生成概率
    const adjustedWeights = {}
    Object.keys(weights).forEach(level => {
      const levelNum = parseInt(level)
      const bias = levelNum >= 3 ? spacePressureConfig.LARGE_BALL_BIAS : 0.5
      adjustedWeights[level] = Math.floor(weights[level] * bias)
    })
    
    return adjustedWeights
  }
  
  /**
   * 记录游戏结果（用于自适应难度）
   */
  recordGameResult(success, finalScore, gameTime) {
    const result = {
      success,
      finalScore,
      gameTime,
      timestamp: Date.now()
    }
    
    this.gameStats.recentGames.push(result)
    
    // 只保留最近的游戏记录
    const maxRecords = GameConfig.DIFFICULTY.FAILURE_CONTROL.DYNAMIC_ADJUSTMENT.MONITOR_WINDOW
    if (this.gameStats.recentGames.length > maxRecords) {
      this.gameStats.recentGames.shift()
    }
    
    console.log(`📊 游戏结果记录: ${success ? '成功' : '失败'}, 分数: ${finalScore}, 时间: ${gameTime}s`)
  }
}
