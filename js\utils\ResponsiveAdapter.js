/**
 * 响应式适配器
 * 统一处理不同设备尺寸下的UI适配
 */

import { Y2KUIConfig, getResponsiveScale, getResponsiveFontSize, getResponsiveButtonSize, getDeviceSpecificConfig } from '../config/Y2KUIConfig.js'

export class ResponsiveAdapter {
  constructor(screenWidth, screenHeight) {
    this.screenWidth = screenWidth
    this.screenHeight = screenHeight
    this.deviceConfig = getDeviceSpecificConfig(screenWidth)
    this.scale = getResponsiveScale(screenWidth)
    
    // 缓存计算结果
    this.cachedFontSizes = new Map()
    this.cachedButtonSizes = new Map()
  }

  /**
   * 获取响应式字体配置
   */
  getResponsiveFontConfig(baseFontConfig) {
    const cacheKey = `${baseFontConfig.size}_${baseFontConfig.weight}_${baseFontConfig.family}`
    
    if (this.cachedFontSizes.has(cacheKey)) {
      return this.cachedFontSizes.get(cacheKey)
    }
    
    const responsiveConfig = {
      ...baseFontConfig,
      size: getResponsiveFontSize(baseFontConfig.size, this.screenWidth),
      strokeWidth: Math.max(1, Math.floor((baseFontConfig.strokeWidth || 1) * this.scale))
    }
    
    this.cachedFontSizes.set(cacheKey, responsiveConfig)
    return responsiveConfig
  }

  /**
   * 获取响应式按钮尺寸
   */
  getResponsiveButtonSize(baseWidth, baseHeight) {
    const cacheKey = `${baseWidth}_${baseHeight}`
    
    if (this.cachedButtonSizes.has(cacheKey)) {
      return this.cachedButtonSizes.get(cacheKey)
    }
    
    const responsiveSize = getResponsiveButtonSize(baseWidth, baseHeight, this.screenWidth)
    this.cachedButtonSizes.set(cacheKey, responsiveSize)
    return responsiveSize
  }

  /**
   * 获取响应式间距
   */
  getResponsiveSpacing(baseSpacing) {
    return Math.max(5, Math.floor(baseSpacing * this.scale))
  }

  /**
   * 获取响应式边框宽度
   */
  getResponsiveBorderWidth(baseBorderWidth = 2) {
    return Math.max(1, Math.floor(baseBorderWidth * this.scale))
  }

  /**
   * 获取响应式图标尺寸
   */
  getResponsiveIconSize(baseIconSize = 24) {
    return Math.max(16, Math.floor(baseIconSize * this.scale))
  }

  /**
   * 适配菜单场景UI
   */
  adaptMenuScene(menuScene) {
    // 适配按钮
    menuScene.buttons.forEach(button => {
      const responsiveSize = this.getResponsiveButtonSize(button.width, button.height)
      button.width = responsiveSize.width
      button.height = responsiveSize.height
    })
    
    // 适配音频按钮
    const audioButtonSize = this.getResponsiveButtonSize(
      menuScene.audioButton.width, 
      menuScene.audioButton.height
    )
    menuScene.audioButton.width = audioButtonSize.width
    menuScene.audioButton.height = audioButtonSize.height
    
    return menuScene
  }

  /**
   * 适配游戏场景UI
   */
  adaptGameScene(gameScene) {
    // 适配菜单按钮
    const menuButtonSize = this.getResponsiveButtonSize(
      gameScene.menuButton.width,
      gameScene.menuButton.height
    )
    gameScene.menuButton.width = menuButtonSize.width
    gameScene.menuButton.height = menuButtonSize.height
    
    // 适配计分器
    const scoreDisplaySize = this.getResponsiveButtonSize(
      gameScene.scoreDisplay.width,
      gameScene.scoreDisplay.height
    )
    gameScene.scoreDisplay.width = scoreDisplaySize.width
    gameScene.scoreDisplay.height = scoreDisplaySize.height
    
    // 适配道具按钮
    const powerUpSize = this.getResponsiveButtonSize(
      gameScene.powerUpItem.width,
      gameScene.powerUpItem.height
    )
    gameScene.powerUpItem.width = powerUpSize.width
    gameScene.powerUpItem.height = powerUpSize.height
    
    return gameScene
  }

  /**
   * 适配游戏结束场景UI
   */
  adaptGameOverScene(gameOverScene) {
    // 适配按钮
    gameOverScene.buttons.forEach(button => {
      const responsiveSize = this.getResponsiveButtonSize(button.width, button.height)
      button.width = responsiveSize.width
      button.height = responsiveSize.height
    })
    
    // 适配音频按钮
    const audioButtonSize = this.getResponsiveButtonSize(
      gameOverScene.audioButton.width,
      gameOverScene.audioButton.height
    )
    gameOverScene.audioButton.width = audioButtonSize.width
    gameOverScene.audioButton.height = audioButtonSize.height
    
    return gameOverScene
  }

  /**
   * 获取安全区域配置
   */
  getSafeAreaConfig() {
    return {
      top: Math.max(20, Math.floor(Y2KUIConfig.LAYOUT.SAFE_AREA.top * this.scale)),
      bottom: Math.max(20, Math.floor(Y2KUIConfig.LAYOUT.SAFE_AREA.bottom * this.scale)),
      left: Math.max(10, Math.floor(Y2KUIConfig.LAYOUT.SAFE_AREA.left * this.scale)),
      right: Math.max(10, Math.floor(Y2KUIConfig.LAYOUT.SAFE_AREA.right * this.scale))
    }
  }

  /**
   * 检查触摸目标是否符合可访问性标准
   */
  validateTouchTarget(width, height) {
    const minSize = Y2KUIConfig.ACCESSIBILITY.MIN_TOUCH_TARGET * this.scale
    return width >= minSize && height >= minSize
  }

  /**
   * 获取设备特定的UI调整
   */
  getDeviceSpecificAdjustments() {
    switch (this.deviceConfig.deviceType) {
      case 'small':
        return {
          reduceAnimations: true,
          simplifyEffects: true,
          increaseTouchTargets: true,
          fontSizeBoost: 1.1
        }
      case 'medium':
        return {
          reduceAnimations: false,
          simplifyEffects: false,
          increaseTouchTargets: false,
          fontSizeBoost: 1.0
        }
      case 'large':
        return {
          reduceAnimations: false,
          simplifyEffects: false,
          increaseTouchTargets: false,
          fontSizeBoost: 0.95
        }
      case 'xlarge':
        return {
          reduceAnimations: false,
          simplifyEffects: false,
          increaseTouchTargets: false,
          fontSizeBoost: 0.9
        }
      default:
        return {
          reduceAnimations: false,
          simplifyEffects: false,
          increaseTouchTargets: false,
          fontSizeBoost: 1.0
        }
    }
  }

  /**
   * 更新屏幕尺寸（当设备旋转或窗口大小改变时）
   */
  updateScreenSize(newWidth, newHeight) {
    this.screenWidth = newWidth
    this.screenHeight = newHeight
    this.deviceConfig = getDeviceSpecificConfig(newWidth)
    this.scale = getResponsiveScale(newWidth)
    
    // 清除缓存
    this.cachedFontSizes.clear()
    this.cachedButtonSizes.clear()
  }

  /**
   * 获取响应式网格配置
   */
  getResponsiveGridConfig() {
    const baseColumns = Y2KUIConfig.LAYOUT.GRID.columns
    const baseGutter = Y2KUIConfig.LAYOUT.GRID.gutterWidth
    
    return {
      columns: this.deviceConfig.deviceType === 'small' ? Math.max(6, baseColumns - 2) : baseColumns,
      gutterWidth: this.getResponsiveSpacing(baseGutter),
      containerMaxWidth: Math.min(this.screenWidth - 40, Y2KUIConfig.LAYOUT.GRID.containerMaxWidth * this.scale)
    }
  }

  /**
   * 计算响应式布局位置
   */
  calculateResponsiveLayout(elements) {
    const safeArea = this.getSafeAreaConfig()
    const availableWidth = this.screenWidth - safeArea.left - safeArea.right
    const availableHeight = this.screenHeight - safeArea.top - safeArea.bottom
    
    return elements.map(element => ({
      ...element,
      x: safeArea.left + (element.x / this.screenWidth) * availableWidth,
      y: safeArea.top + (element.y / this.screenHeight) * availableHeight,
      width: (element.width / this.screenWidth) * availableWidth,
      height: (element.height / this.screenHeight) * availableHeight
    }))
  }
}

// 单例模式，确保全局只有一个响应式适配器实例
let responsiveAdapterInstance = null

export function getResponsiveAdapter(screenWidth, screenHeight) {
  if (!responsiveAdapterInstance || 
      responsiveAdapterInstance.screenWidth !== screenWidth || 
      responsiveAdapterInstance.screenHeight !== screenHeight) {
    responsiveAdapterInstance = new ResponsiveAdapter(screenWidth, screenHeight)
  }
  return responsiveAdapterInstance
}
