// 微信小游戏适配器
// 提供浏览器API的兼容性

// 全局对象
if (typeof window === 'undefined') {
  global.window = global
}

// requestAnimationFrame
if (!window.requestAnimationFrame) {
  window.requestAnimationFrame = function(callback) {
    return setTimeout(callback, 1000 / 60)
  }
}

if (!window.cancelAnimationFrame) {
  window.cancelAnimationFrame = function(id) {
    clearTimeout(id)
  }
}

// Canvas API
if (!window.HTMLCanvasElement) {
  window.HTMLCanvasElement = function() {}
}

// 触摸事件
window.TouchEvent = function(type, options) {
  this.type = type
  this.touches = options.touches || []
  this.changedTouches = options.changedTouches || []
}

// 触摸点
window.Touch = function(options) {
  this.clientX = options.clientX || 0
  this.clientY = options.clientY || 0
  this.pageX = options.pageX || options.clientX || 0
  this.pageY = options.pageY || options.clientY || 0
  this.identifier = options.identifier || 0
}

// 事件对象
window.Event = function(type, options) {
  this.type = type
  this.target = options.target || null
  this.currentTarget = options.currentTarget || null
  this.preventDefault = function() {}
  this.stopPropagation = function() {}
}

// 鼠标事件
window.MouseEvent = function(type, options) {
  window.Event.call(this, type, options)
  this.clientX = options.clientX || 0
  this.clientY = options.clientY || 0
  this.button = options.button || 0
}

// 键盘事件
window.KeyboardEvent = function(type, options) {
  window.Event.call(this, type, options)
  this.key = options.key || ''
  this.keyCode = options.keyCode || 0
  this.which = options.which || options.keyCode || 0
}

// 音频API
if (!window.Audio) {
  window.Audio = function(src) {
    this.src = src
    this.play = function() {
      // 微信小游戏音频播放
      if (wx.createInnerAudioContext) {
        const audio = wx.createInnerAudioContext()
        audio.src = this.src
        audio.play()
      }
    }
  }
}

// 图片加载
if (!window.Image) {
  window.Image = function() {
    this.src = ''
    this.onload = null
    this.onerror = null
    this.width = 0
    this.height = 0
  }
}

// 本地存储
if (!window.localStorage) {
  window.localStorage = {
    getItem: function(key) {
      try {
        return wx.getStorageSync(key)
      } catch (e) {
        return null
      }
    },
    setItem: function(key, value) {
      try {
        wx.setStorageSync(key, value)
      } catch (e) {
        console.error('Storage setItem failed:', e)
      }
    },
    removeItem: function(key) {
      try {
        wx.removeStorageSync(key)
      } catch (e) {
        console.error('Storage removeItem failed:', e)
      }
    },
    clear: function() {
      try {
        wx.clearStorageSync()
      } catch (e) {
        console.error('Storage clear failed:', e)
      }
    }
  }
}

// 控制台
if (!window.console) {
  window.console = {
    log: function() {
      console.log.apply(console, arguments)
    },
    error: function() {
      console.error.apply(console, arguments)
    },
    warn: function() {
      console.warn.apply(console, arguments)
    },
    info: function() {
      console.info.apply(console, arguments)
    }
  }
}

// 数学函数
if (!window.Math) {
  window.Math = Math
}

// 随机数
if (!window.Math.random) {
  window.Math.random = function() {
    return Math.random()
  }
}

// 三角函数
if (!window.Math.sin) {
  window.Math.sin = Math.sin
  window.Math.cos = Math.cos
  window.Math.tan = Math.tan
  window.Math.asin = Math.asin
  window.Math.acos = Math.acos
  window.Math.atan = Math.atan
  window.Math.atan2 = Math.atan2
}

// 其他数学函数
if (!window.Math.sqrt) {
  window.Math.sqrt = Math.sqrt
  window.Math.pow = Math.pow
  window.Math.abs = Math.abs
  window.Math.floor = Math.floor
  window.Math.ceil = Math.ceil
  window.Math.round = Math.round
  window.Math.min = Math.min
  window.Math.max = Math.max
  window.Math.PI = Math.PI
}

// 数组方法
if (!Array.prototype.forEach) {
  Array.prototype.forEach = function(callback, thisArg) {
    for (let i = 0; i < this.length; i++) {
      callback.call(thisArg, this[i], i, this)
    }
  }
}

if (!Array.prototype.filter) {
  Array.prototype.filter = function(callback, thisArg) {
    const result = []
    for (let i = 0; i < this.length; i++) {
      if (callback.call(thisArg, this[i], i, this)) {
        result.push(this[i])
      }
    }
    return result
  }
}

if (!Array.prototype.map) {
  Array.prototype.map = function(callback, thisArg) {
    const result = []
    for (let i = 0; i < this.length; i++) {
      result.push(callback.call(thisArg, this[i], i, this))
    }
    return result
  }
}

if (!Array.prototype.find) {
  Array.prototype.find = function(callback, thisArg) {
    for (let i = 0; i < this.length; i++) {
      if (callback.call(thisArg, this[i], i, this)) {
        return this[i]
      }
    }
    return undefined
  }
}

if (!Array.prototype.reduce) {
  Array.prototype.reduce = function(callback, initialValue) {
    let accumulator = initialValue === undefined ? this[0] : initialValue
    const startIndex = initialValue === undefined ? 1 : 0
    
    for (let i = startIndex; i < this.length; i++) {
      accumulator = callback(accumulator, this[i], i, this)
    }
    
    return accumulator
  }
}

// 字符串方法
if (!String.prototype.startsWith) {
  String.prototype.startsWith = function(searchString, position) {
    position = position || 0
    return this.substr(position, searchString.length) === searchString
  }
}

if (!String.prototype.endsWith) {
  String.prototype.endsWith = function(searchString, length) {
    if (length === undefined || length > this.length) {
      length = this.length
    }
    return this.substring(length - searchString.length, length) === searchString
  }
}

// 对象方法
if (!Object.assign) {
  Object.assign = function(target) {
    for (let i = 1; i < arguments.length; i++) {
      const source = arguments[i]
      for (const key in source) {
        if (source.hasOwnProperty(key)) {
          target[key] = source[key]
        }
      }
    }
    return target
  }
}

// 导出全局对象
export default window 