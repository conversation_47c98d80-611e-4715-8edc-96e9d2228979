import { MenuScene } from './scenes/MenuScene.js'
import { GameScene } from './scenes/GameScene.js'
import { GameOverScene } from './scenes/GameOverScene.js'
import { GameData } from './utils/GameData.js'
import { AudioManager } from './utils/AudioManager.js'
import { LoadingScreen } from './utils/LoadingScreen.js'
import { ResourceLoader } from './utils/ResourceLoader.js'

import { physicsManager } from './libs/physics-manager.js'

export default class Main {
  constructor() {
    // 设置全局引用，供其他模块使用
    window.gameMain = this
    
    this.canvas = wx.createCanvas()
    this.ctx = this.canvas.getContext('2d')
    this.screenWidth = this.canvas.width
    this.screenHeight = this.canvas.height
    
    console.log(`🎨 画布初始化: ${this.screenWidth}x${this.screenHeight}`)
    console.log('🎨 渲染上下文:', this.ctx)
    
    this.gameData = new GameData()
    this.audioManager = new AudioManager()
    this.loadingScreen = new LoadingScreen(this)
    this.resourceLoader = new ResourceLoader()
    
    this.eventManager = new EventManager()
    this.initScenes()
    this.bindGlobalEvents()
    this.initResourceLoading()
    this.gameLoop()
    

  }

  initScenes() {
    this.menuScene = new MenuScene(this)
    this.gameScene = new GameScene(this)
    this.gameOverScene = new GameOverScene(this)
    this.currentScene = null
  }



  bindGlobalEvents() {
    wx.onTouchStart((e) => {
      this.eventManager.handleTouchStart(e)
    })
    wx.onTouchMove((e) => {
      this.eventManager.handleTouchMove(e)
    })
    wx.onTouchEnd((e) => {
      this.eventManager.handleTouchEnd(e)
    })
  }

  // 初始化资源加载
  initResourceLoading() {
    // 显示加载页面
    this.loadingScreen.show('正在准备资源...')
    
    // 设置资源加载器的回调
    this.resourceLoader.setOnProgress((progress, message) => {
      this.loadingScreen.updateProgress(progress, message)
    })
    
    this.resourceLoader.setOnComplete(() => {
      this.loadingScreen.updateProgress(1, '加载完成！')
      setTimeout(() => {
        this.loadingScreen.hide()
        this.switchScene('menu')
      }, 500)
    })
    
    this.resourceLoader.setOnError((error, retryCount) => {
      this.loadingScreen.setError(`加载失败: ${error.message}`)
      this.loadingScreen.setRetryCount(retryCount)
    })
    
    // 设置加载页面的重试回调
    this.loadingScreen.setOnRetry(() => {
      this.resourceLoader.retry()
    })
    
    // 开始加载资源
    this.resourceLoader.startLoading()
  }

  async switchScene(sceneName) {
    console.log(`切换场景: ${sceneName}`)
    if (this.currentScene && this.currentScene.cleanupEvents) {
      this.currentScene.cleanupEvents()
    }
    this.currentScene = null
    switch (sceneName) {
      case 'menu':
        this.currentScene = this.menuScene
        console.log('当前场景: 菜单')
        // 切换到菜单BGM
        this.audioManager.switchBGMByGameState('menu')
        break
      case 'game':
        this.currentScene = this.gameScene
        console.log('当前场景: 游戏')
        // 切换到游戏BGM
        this.audioManager.switchBGMByGameState('gameplay')
        await this.gameScene.start()
        break
      case 'gameOver':
        this.currentScene = this.gameOverScene
        console.log('当前场景: 游戏结束')
        // 根据游戏结果切换BGM
        const isWin = this.gameData.score >= 10000 // 假设10000分为胜利条件
        this.audioManager.switchBGMByGameState(isWin ? 'win' : 'lose')
        break
    }
    this.eventManager.setCurrentScene(this.currentScene)
  }

  gameLoop() {
    const loop = () => {
      // 清空画布
      this.ctx.clearRect(0, 0, this.screenWidth, this.screenHeight)
      

      
      // 渲染当前场景
      if (this.currentScene) {
        this.currentScene.render(this.ctx)
      }
      
      // 渲染加载页面（如果可见）
      this.loadingScreen.render(this.ctx)
      
      // 更新当前场景
      if (this.currentScene && this.currentScene.update) {
        this.currentScene.update()
      }
      
      requestAnimationFrame(loop)
    }
    loop()
  }



  getCurrentSceneInfo() {
    return {
      sceneName: this.currentScene ? this.currentScene.constructor.name : 'null',
      hasTouchHandlers: this.currentScene ? {
        touchStart: !!this.currentScene.handleTouchStart,
        touchMove: !!this.currentScene.handleTouchMove,
        touchEnd: !!this.currentScene.handleTouchEnd
      } : null
    }
  }
}

class EventManager {
  constructor() {
    this.currentScene = null
  }
  
  setCurrentScene(scene) {
    this.currentScene = scene
  }
  
  handleTouchStart(e) {
    // 如果加载页面可见，优先处理加载页面的触摸事件
    if (this.currentScene && this.currentScene.main && this.currentScene.main.loadingScreen && this.currentScene.main.loadingScreen.isVisible) {
      const touch = e.touches[0]
      this.currentScene.main.loadingScreen.handleTouch(touch.clientX, touch.clientY)
      return
    }
    
    if (this.currentScene && this.currentScene.handleTouchStart) {
      const touch = e.touches[0]
      this.currentScene.handleTouchStart(touch.clientX, touch.clientY)
    }
  }
  
  handleTouchMove(e) {
    if (this.currentScene && this.currentScene.handleTouchMove) {
      const touch = e.touches[0]
      this.currentScene.handleTouchMove(touch.clientX, touch.clientY)
    }
  }
  
  handleTouchEnd(e) {
    if (this.currentScene && this.currentScene.handleTouchEnd) {
      this.currentScene.handleTouchEnd()
    }
  }
  

}