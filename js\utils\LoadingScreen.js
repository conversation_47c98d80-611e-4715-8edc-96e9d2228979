import { Y2KUIConfig } from '../config/Y2KUIConfig.js'

export class LoadingScreen {
  constructor(main) {
    this.main = main
    this.screenWidth = main.screenWidth
    this.screenHeight = main.screenHeight
    this.isVisible = false
    this.progress = 0
    this.message = '正在加载资源...'
    this.error = ''
    this.retryCount = 0
    this.maxRetry = 3
    this.isLoading = false
    this.onComplete = null
    this.onRetry = null

    // 背景图片
    this.backgroundImage = null
    this.backgroundImageLoaded = false

    // 像素效果系统（延迟获取）
    this.pixelEffects = null

    // 加载背景图片
    this.loadBackgroundImage()
  }

  /**
   * 加载背景图片
   */
  loadBackgroundImage() {
    try {
      this.backgroundImage = wx.createImage()
      this.backgroundImage.onload = () => {
        this.backgroundImageLoaded = true
        console.log('加载页面背景图片加载成功: loading.png')
      }
      this.backgroundImage.onerror = (error) => {
        console.warn('加载页面背景图片加载失败: loading.png', error)
        this.backgroundImageLoaded = false
      }
      this.backgroundImage.src = 'game_media/images/loading.png'
    } catch (error) {
      console.warn('创建加载页面背景图片对象失败: loading.png', error)
      this.backgroundImageLoaded = false
    }
  }

  show(message = '正在加载资源...') {
    this.isVisible = true
    this.message = message
    this.progress = 0
    this.error = ''
    this.retryCount = 0
    this.isLoading = false
  }

  updateProgress(progress, message) {
    this.progress = progress
    if (message) {
      this.message = message
    }
  }

  setError(errorMsg) {
    this.error = errorMsg
    this.isLoading = false
  }

  setRetryCount(count) {
    this.retryCount = count
  }

  setOnComplete(cb) {
    this.onComplete = cb
  }

  setOnRetry(cb) {
    this.onRetry = cb
  }

  hide() {
    this.isVisible = false
  }

  render(ctx) {
    if (!this.isVisible) return

    // 延迟获取pixelEffects（当场景初始化后）
    if (!this.pixelEffects && this.main.menuScene && this.main.menuScene.pixelEffects) {
      this.pixelEffects = this.main.menuScene.pixelEffects
    }
    
    // 绘制背景图片
    if (this.backgroundImage && this.backgroundImageLoaded && 
        this.backgroundImage.complete && this.backgroundImage.naturalWidth > 0) {
      
      // 计算图片绘制参数，保持宽高比并覆盖整个屏幕
      const imageAspectRatio = this.backgroundImage.naturalWidth / this.backgroundImage.naturalHeight
      const screenAspectRatio = this.screenWidth / this.screenHeight
      
      let drawWidth, drawHeight, drawX, drawY
      
      if (imageAspectRatio > screenAspectRatio) {
        // 图片较宽，以高度为准
        drawHeight = this.screenHeight
        drawWidth = this.screenHeight * imageAspectRatio
        drawX = (this.screenWidth - drawWidth) / 2
        drawY = 0
      } else {
        // 图片较高，以宽度为准
        drawWidth = this.screenWidth
        drawHeight = this.screenWidth / imageAspectRatio
        drawX = 0
        drawY = (this.screenHeight - drawHeight) / 2
      }
      
      // 绘制背景图片
      ctx.drawImage(this.backgroundImage, drawX, drawY, drawWidth, drawHeight)
    } else {
      // 如果图片未加载，使用半透明黑色背景作为备用
      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
      ctx.fillRect(0, 0, this.screenWidth, this.screenHeight)
    }
    
    // 玻璃拟态加载框
    const boxWidth = 320
    const boxHeight = 180
    const boxX = (this.screenWidth - boxWidth) / 2
    const boxY = (this.screenHeight - boxHeight) / 2

    // 绘制加载框（玻璃拟态风格或传统风格）
    if (this.pixelEffects) {
      // 使用玻璃拟态风格
      const boxConfig = {
        color: Y2KUIConfig.COLORS.ALPHA.BACKGROUND_OVERLAY,
        borderColor: Y2KUIConfig.COLORS.CYBER_CYAN,
        isHovered: false,
        glassStyle: true
      }

      this.pixelEffects.drawPixelButton(
        this.screenWidth / 2, this.screenHeight / 2,
        boxWidth, boxHeight,
        boxConfig
      )
    } else {
      // 回退到传统风格
      ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
      ctx.fillRect(boxX + 3, boxY + 3, boxWidth, boxHeight)

      ctx.fillStyle = '#2C3E50'
      ctx.fillRect(boxX, boxY, boxWidth, boxHeight)
      ctx.strokeStyle = '#34495E'
      ctx.lineWidth = 3
      ctx.strokeRect(boxX, boxY, boxWidth, boxHeight)
    }
    // 标题渲染（Y2K风格或传统风格）
    if (this.pixelEffects) {
      // Y2K风格标题
      this.pixelEffects.drawPixelText(
        '< LOADING SYSTEM >',
        this.screenWidth / 2, boxY + 40,
        Y2KUIConfig.FONTS.PIXEL_TITLE,
        Y2KUIConfig.COLORS.CYBER_CYAN,
        Y2KUIConfig.COLORS.PIXEL_BLACK,
        true, // 启用玻璃风格
        false // 不是悬停状态
      )
    } else {
      // 传统风格标题
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
      ctx.font = 'bold 24px Arial'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText('资源加载中', this.screenWidth / 2 + 1, boxY + 41)

      ctx.fillStyle = 'white'
      ctx.fillText('资源加载中', this.screenWidth / 2, boxY + 40)
    }
    
    // 消息渲染（Y2K风格或传统风格）
    if (this.pixelEffects) {
      // Y2K风格消息
      this.pixelEffects.drawPixelText(
        this.message,
        this.screenWidth / 2, boxY + 75,
        Y2KUIConfig.FONTS.SYSTEM_HINT,
        Y2KUIConfig.COLORS.MATRIX_GREEN,
        null, // 不需要描边
        true, // 启用玻璃风格
        false // 不是悬停状态
      )
    } else {
      // 传统风格消息
      ctx.font = '16px Arial'
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
      ctx.fillText(this.message, this.screenWidth / 2 + 1, boxY + 76)

      ctx.fillStyle = 'rgba(255,255,255,0.9)'
      ctx.fillText(this.message, this.screenWidth / 2, boxY + 75)
    }
    // 进度条渲染（玻璃拟态风格或传统风格）
    const progressBarWidth = 220
    const progressBarHeight = 22
    const progressBarX = (this.screenWidth - progressBarWidth) / 2
    const progressBarY = boxY + 110

    if (this.pixelEffects) {
      // 玻璃拟态进度条
      const progressBgConfig = {
        color: 'rgba(255,255,255,0.1)',
        borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
        isHovered: false,
        glassStyle: true
      }

      this.pixelEffects.drawPixelButton(
        this.screenWidth / 2, progressBarY + progressBarHeight / 2,
        progressBarWidth, progressBarHeight,
        progressBgConfig
      )

      // 进度条填充
      if (this.progress > 0) {
        const fillWidth = progressBarWidth * this.progress
        const progressFillConfig = {
          color: Y2KUIConfig.COLORS.MATRIX_GREEN,
          borderColor: Y2KUIConfig.COLORS.CYBER_CYAN,
          isHovered: false,
          glassStyle: true
        }

        this.pixelEffects.drawPixelButton(
          progressBarX + fillWidth / 2, progressBarY + progressBarHeight / 2,
          fillWidth, progressBarHeight,
          progressFillConfig
        )
      }
    } else {
      // 传统风格进度条
      ctx.fillStyle = 'rgba(255,255,255,0.3)'
      ctx.fillRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight)
      ctx.fillStyle = '#2ECC71'
      ctx.fillRect(progressBarX, progressBarY, progressBarWidth * this.progress, progressBarHeight)
      ctx.strokeStyle = 'rgba(255,255,255,0.5)'
      ctx.lineWidth = 1
      ctx.strokeRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight)
    }
    // 百分比渲染（Y2K风格或传统风格）
    if (this.pixelEffects) {
      // Y2K风格百分比
      this.pixelEffects.drawPixelText(
        `${Math.round(this.progress * 100)}%`,
        this.screenWidth / 2, progressBarY + 36,
        Y2KUIConfig.FONTS.DIGITAL_SCORE,
        Y2KUIConfig.COLORS.CYBER_CYAN,
        null, // 不需要描边
        true, // 启用玻璃风格
        false // 不是悬停状态
      )
    } else {
      // 传统风格百分比
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
      ctx.font = '14px Arial'
      ctx.fillText(`${Math.round(this.progress * 100)}%`, this.screenWidth / 2 + 1, progressBarY + 37)

      ctx.fillStyle = 'white'
      ctx.fillText(`${Math.round(this.progress * 100)}%`, this.screenWidth / 2, progressBarY + 36)
    }
    // 错误提示
    if (this.error) {
      if (this.pixelEffects) {
        // Y2K风格错误提示
        this.pixelEffects.drawPixelText(
          this.error,
          this.screenWidth / 2, boxY + boxHeight - 35,
          Y2KUIConfig.FONTS.SYSTEM_HINT,
          Y2KUIConfig.COLORS.NEON_PINK,
          null, // 不需要描边
          true, // 启用玻璃风格
          false // 不是悬停状态
        )

        if (this.retryCount >= this.maxRetry) {
          // 玻璃拟态重试按钮
          const retryButtonConfig = {
            color: Y2KUIConfig.COLORS.MATRIX_GREEN,
            borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
            isHovered: false,
            glassStyle: true
          }

          this.pixelEffects.drawPixelButton(
            this.screenWidth / 2, boxY + boxHeight - 14,
            120, 28,
            retryButtonConfig
          )

          this.pixelEffects.drawPixelText(
            '> 点击重试',
            this.screenWidth / 2, boxY + boxHeight - 14,
            Y2KUIConfig.FONTS.PIXEL_BUTTON,
            'rgba(255, 255, 255, 0.9)',
            null,
            true,
            false
          )
        } else {
          this.pixelEffects.drawPixelText(
            `正在自动重试(${this.retryCount}/${this.maxRetry})...`,
            this.screenWidth / 2, boxY + boxHeight - 14,
            Y2KUIConfig.FONTS.SYSTEM_HINT,
            Y2KUIConfig.COLORS.LIQUID_ORANGE,
            null,
            true,
            false
          )
        }
      } else {
        // 传统风格错误提示
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
        ctx.font = 'bold 16px Arial'
        ctx.fillText(this.error, this.screenWidth / 2 + 1, boxY + boxHeight - 34)

        ctx.fillStyle = '#FF5555'
        ctx.fillText(this.error, this.screenWidth / 2, boxY + boxHeight - 35)

        if (this.retryCount >= this.maxRetry) {
          // 传统重试按钮
          ctx.fillStyle = '#2ECC71'
          ctx.fillRect(this.screenWidth / 2 - 60, boxY + boxHeight - 28, 120, 28)
          ctx.fillStyle = 'white'
          ctx.font = 'bold 16px Arial'
          ctx.fillText('点击重试', this.screenWidth / 2, boxY + boxHeight - 14)
        } else {
          ctx.font = '14px Arial'
          ctx.fillStyle = 'rgba(255,255,255,0.7)'
          ctx.fillText(`正在自动重试(${this.retryCount}/${this.maxRetry})...`, this.screenWidth / 2, boxY + boxHeight - 14)
        }
      }
    }
  }

  // 处理点击事件（用于手动重试按钮）
  handleTouch(x, y) {
    if (!this.error || this.retryCount < this.maxRetry) return
    // 检查是否点在按钮上
    const boxWidth = 320
    const boxHeight = 180
    const boxX = (this.screenWidth - boxWidth) / 2
    const boxY = (this.screenHeight - boxHeight) / 2
    const btnX = this.screenWidth / 2 - 60
    const btnY = boxY + boxHeight - 28
    const btnW = 120
    const btnH = 28
    if (x >= btnX && x <= btnX + btnW && y >= btnY && y <= btnY + btnH) {
      this.error = ''
      this.isLoading = true
      if (this.onRetry) this.onRetry()
    }
  }
} 