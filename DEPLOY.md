# 合成大西瓜 - 部署指南

## 准备工作

### 1. 微信小游戏账号
- 注册微信公众平台账号
- 申请小游戏类目
- 获取小游戏AppID

### 2. 开发环境
- 下载并安装[微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 确保开发者工具版本支持小游戏开发

## 项目配置

### 1. 修改AppID
在 `project.config.json` 文件中修改 `appid` 字段：
```json
{
  "appid": "你的小游戏AppID"
}
```

### 2. 添加游戏资源
将音频和图片文件放入 `game_media` 目录：
```
game_media/
├── audio/
│   ├── bgm.mp3          # 背景音乐
│   ├── merge.mp3        # 合成音效
│   ├── drop.mp3         # 掉落音效
│   ├── gameover.mp3     # 游戏结束音效
│   ├── levelup.mp3      # 升级音效
│   └── click.mp3        # 点击音效
└── images/
    ├── fruits/          # 水果图片（可选）
    ├── ui/              # UI元素（可选）
    └── effects/         # 特效图片（可选）
```

### 3. 自定义游戏参数
在 `js/config/GameConfig.js` 中调整游戏参数：
- 水果配置
- 物理参数
- 音效设置
- UI样式

## 开发调试

### 1. 导入项目
1. 打开微信开发者工具
2. 选择"小游戏"项目
3. 导入项目目录
4. 填入AppID

### 2. 预览测试
- 点击"预览"按钮生成二维码
- 使用微信扫码在手机上测试
- 或使用模拟器进行调试

### 3. 真机调试
- 点击"真机调试"按钮
- 扫码在真机上调试
- 查看控制台日志

## 代码优化

### 1. 代码压缩
在发布前压缩JavaScript代码：
```bash
# 使用工具如uglify-js
uglifyjs js/**/*.js -o dist/game.min.js
```

### 2. 资源优化
- 压缩图片文件（PNG、JPG）
- 压缩音频文件（MP3）
- 移除未使用的资源

### 3. 性能优化
- 减少DOM操作
- 优化渲染循环
- 合理使用内存

## 发布流程

### 1. 代码审查
- 检查代码质量
- 确保无敏感信息
- 测试所有功能

### 2. 提交审核
1. 登录微信公众平台
2. 进入小游戏管理
3. 上传代码包
4. 填写游戏信息
5. 提交审核

### 3. 审核材料
准备以下材料：
- 游戏截图（至少3张）
- 游戏介绍
- 游戏分类
- 隐私政策（如需要）

### 4. 发布上线
- 审核通过后发布
- 设置游戏可见性
- 配置分享功能

## 版本更新

### 1. 代码更新
- 修改版本号
- 更新代码
- 测试新功能

### 2. 重新发布
- 上传新版本代码
- 提交审核
- 发布更新

## 常见问题

### 1. 审核被拒
常见原因：
- 游戏内容不符合规范
- 代码存在bug
- 资源文件过大
- 缺少必要信息

### 2. 性能问题
优化建议：
- 减少资源文件大小
- 优化渲染性能
- 合理使用内存

### 3. 兼容性问题
测试建议：
- 在不同设备上测试
- 测试不同微信版本
- 检查API兼容性

## 技术支持

如遇到问题，可以：
1. 查看微信小游戏官方文档
2. 在开发者社区提问
3. 联系技术支持

## 注意事项

1. **版权问题**：确保使用的资源有合法版权
2. **隐私保护**：遵守用户隐私保护规定
3. **内容合规**：确保游戏内容符合平台规范
4. **性能要求**：满足微信小游戏的性能要求

---

祝你的合成大西瓜游戏发布成功！🍉 