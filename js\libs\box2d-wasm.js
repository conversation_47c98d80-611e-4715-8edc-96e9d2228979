/**
 * Box2D WASM 物理引擎加载器 - 支持微信小游戏环境
 * 提供 Box2D WASM 和简化物理引擎两种模式
 */

// 路径配置（根据实际项目结构调整）
const PATH_CONFIG = {
  box2dWasm: '/js/libs/Box2D.wasm'  // 供 readFile 用，必须绝对
};

// 简化的 Box2D 工厂函数（避免 require 问题）
function createBox2DFactory() {
  return function(options) {
    return new Promise((resolve, reject) => {
      // 这里可以添加简化的 Box2D 实现
      // 暂时返回一个基本的物理引擎对象
      const box2d = {
        b2World: function(gravity) {
          this.gravity = gravity;
          this.bodies = [];
          this.step = function() {
            // 简化的物理步进
            this.bodies.forEach(body => {
              if (body.type === 'dynamic') {
                body.velocity.y += this.gravity.y * 0.016; // 16ms timestep
                body.position.x += body.velocity.x * 0.016;
                body.position.y += body.velocity.y * 0.016;
              }
            });
          };
        },
        b2BodyDef: function() {
          this.type = 'dynamic';
          this.position = { x: 0, y: 0 };
          this.velocity = { x: 0, y: 0 };
        },
        b2CircleShape: function() {
          this.radius = 1;
        },
        b2FixtureDef: function() {
          this.shape = null;
          this.density = 1;
          this.friction = 0.2;
          this.restitution = 0.5;
        },
        b2Vec2: function(x, y) {
          this.x = x || 0;
          this.y = y || 0;
        }
      };
      resolve(box2d);
    });
  };
}

let box2dInstance = null;
let box2dInitPromise = null;

/**
 * 加载 Box2D 的函数
 */
function loadBox2D() {
  return new Promise((resolve, reject) => {
    console.log('尝试加载 Box2D WASM...');
    wx.getFileSystemManager().readFile({
      filePath: PATH_CONFIG.box2dWasm,
      encoding: 'binary',
      success: (wasmRes) => {
        try {
          // 使用内置的 Box2D 工厂函数
          const Box2DLoader = createBox2DFactory();
          Box2DLoader({
            wasmBinary: new Uint8Array(wasmRes.data).buffer
          }).then((box2d) => {
            console.log('Box2D WASM 加载成功');
            resolve(box2d);
          }).catch((err) => {
            reject(new Error('WASM 初始化失败: ' + err.message));
          });
        } catch (e) {
          reject(new Error(`加载 Box2D 失败: ${e.message}`));
        }
      },
      fail: (err) => {
        reject(new Error(`读取 Box2D.wasm 失败: ${err.errMsg}`));
      }
    });
  });
}

/**
 * 简化物理引擎初始化（备用方案）
 */
function initFallbackPhysicsEngine() {
  console.log('简化物理引擎初始化成功！');
  return {
    useSimplePhysics: true,
    isLoaded: true,
    world: null,
    bodies: new Map(),
    createFruitBody: function(fruit, x, y) {
      const body = {
        position: { x: x / 30, y: y / 30 },
        velocity: { x: 0, y: 0 },
        force: { x: 0, y: 0 },
        mass: fruit.radius / 10,
        isStatic: false
      };
      this.bodies.set(fruit, body);
      return body;
    },
    removeFruitBody: function(fruit) {
      this.bodies.delete(fruit);
    },
    step: function(timeStep = 1/60) {
      this.bodies.forEach((body, fruit) => {
        if (body.isStatic) return;
        
        // 应用重力
        body.force.y += 9.8 * body.mass;
        
        // 计算加速度
        const acceleration = {
          x: body.force.x / body.mass,
          y: body.force.y / body.mass
        };
        
        // 更新速度
        body.velocity.x += acceleration.x * timeStep;
        body.velocity.y += acceleration.y * timeStep;
        
        // 应用阻尼
        body.velocity.x *= 0.98;
        body.velocity.y *= 0.98;
        
        // 更新位置
        body.position.x += body.velocity.x * timeStep;
        body.position.y += body.velocity.y * timeStep;
        
        // 同步到水果对象
        fruit.x = body.position.x * 30;
        fruit.y = body.position.y * 30;
        
        // 重置力
        body.force.x = 0;
        body.force.y = 0;
      });
    },
    setFruitPosition: function(fruit, x, y) {
      const body = this.bodies.get(fruit);
      if (body) {
        body.position.x = x / 30;
        body.position.y = y / 30;
      }
    },
    applyForceToFruit: function(fruit, forceX, forceY) {
      const body = this.bodies.get(fruit);
      if (body) {
        body.force.x += forceX;
        body.force.y += forceY;
      }
    },
    setMergeCallback: function(callback) {
      this.mergeCallback = callback;
    }
  };
}

/**
 * 统一初始化方法，自动切换Box2D/简化物理引擎
 */
async function initBox2D() {
  if (box2dInstance) return box2dInstance;
  if (box2dInitPromise) return box2dInitPromise;
  box2dInitPromise = loadBox2D()
    .then((box2d) => {
      box2dInstance = box2d;
      return box2d;
    })
    .catch((err) => {
      console.error('Box2D WASM 加载失败，使用简化物理引擎:', err);
      box2dInstance = initFallbackPhysicsEngine();
      return box2dInstance;
    });
  return box2dInitPromise;
}

export { initBox2D, PATH_CONFIG }; 