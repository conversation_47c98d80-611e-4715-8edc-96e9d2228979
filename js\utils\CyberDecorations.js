/**
 * 赛博朋克装饰元素系统
 * 实现怪诞抽象的几何图形和超现实装饰
 */

import { Y2KUIConfig } from '../config/Y2KUIConfig.js'

export class CyberDecorations {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.animationTime = 0
    this.glitchElements = []
    this.floatingSymbols = []
    this.dataStreams = []
    
    this.initializeDecorations()
  }

  /**
   * 初始化装饰元素
   */
  initializeDecorations() {
    // 初始化浮动符号
    for (let i = 0; i < 15; i++) {
      this.floatingSymbols.push({
        symbol: this.getRandomSymbol(),
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        size: Math.random() * 20 + 10,
        rotation: Math.random() * Math.PI * 2,
        rotationSpeed: (Math.random() - 0.5) * 0.02
      })
    }

    // 初始化数据流
    for (let i = 0; i < 8; i++) {
      this.dataStreams.push({
        x: Math.random() * this.canvas.width,
        y: -50,
        speed: Math.random() * 2 + 1,
        length: Math.random() * 100 + 50,
        opacity: Math.random() * 0.5 + 0.2,
        chars: this.generateDataString()
      })
    }
  }

  /**
   * 获取随机赛博朋克符号
   */
  getRandomSymbol() {
    const symbols = Y2KUIConfig.DECORATIONS.CYBER_SYMBOLS
    return symbols[Math.floor(Math.random() * symbols.length)]
  }

  /**
   * 生成数据字符串
   */
  generateDataString() {
    const chars = '01ABCDEF'
    let result = ''
    for (let i = 0; i < 20; i++) {
      result += chars[Math.floor(Math.random() * chars.length)]
    }
    return result
  }

  /**
   * 绘制扭曲几何图形
   */
  drawDistortedGeometry(x, y, size, type = 'triangle') {
    this.ctx.save()
    this.ctx.translate(x, y)
    
    // 添加随机扭曲
    const distortion = Math.sin(this.animationTime * 0.001) * 0.1
    this.ctx.scale(1 + distortion, 1 - distortion)
    
    this.ctx.strokeStyle = Y2KUIConfig.COLORS.CYBER_CYAN
    this.ctx.lineWidth = 2
    this.ctx.fillStyle = Y2KUIConfig.COLORS.ALPHA.GLOW_EFFECT
    
    this.ctx.beginPath()
    
    switch (type) {
      case 'triangle':
        this.drawDistortedTriangle(size)
        break
      case 'hexagon':
        this.drawDistortedHexagon(size)
        break
      case 'circuit':
        this.drawCircuitNode(size)
        break
      case 'crystal':
        this.drawCrystalShape(size)
        break
    }
    
    this.ctx.fill()
    this.ctx.stroke()
    this.ctx.restore()
  }

  /**
   * 绘制扭曲三角形
   */
  drawDistortedTriangle(size) {
    const points = [
      { x: 0, y: -size },
      { x: size * 0.866, y: size * 0.5 },
      { x: -size * 0.866, y: size * 0.5 }
    ]
    
    // 添加噪声扭曲
    points.forEach(point => {
      point.x += (Math.random() - 0.5) * size * 0.2
      point.y += (Math.random() - 0.5) * size * 0.2
    })
    
    this.ctx.moveTo(points[0].x, points[0].y)
    points.forEach(point => {
      this.ctx.lineTo(point.x, point.y)
    })
    this.ctx.closePath()
  }

  /**
   * 绘制扭曲六边形
   */
  drawDistortedHexagon(size) {
    const sides = 6
    const angleStep = (Math.PI * 2) / sides
    
    for (let i = 0; i < sides; i++) {
      const angle = i * angleStep
      const distortedSize = size + Math.sin(this.animationTime * 0.002 + i) * size * 0.2
      const x = Math.cos(angle) * distortedSize
      const y = Math.sin(angle) * distortedSize
      
      if (i === 0) {
        this.ctx.moveTo(x, y)
      } else {
        this.ctx.lineTo(x, y)
      }
    }
    this.ctx.closePath()
  }

  /**
   * 绘制电路节点
   */
  drawCircuitNode(size) {
    // 中心圆
    this.ctx.arc(0, 0, size * 0.3, 0, Math.PI * 2)
    
    // 连接线
    const connections = 4
    for (let i = 0; i < connections; i++) {
      const angle = (i * Math.PI * 2) / connections
      const startX = Math.cos(angle) * size * 0.3
      const startY = Math.sin(angle) * size * 0.3
      const endX = Math.cos(angle) * size
      const endY = Math.sin(angle) * size
      
      this.ctx.moveTo(startX, startY)
      this.ctx.lineTo(endX, endY)
    }
  }

  /**
   * 绘制水晶形状
   */
  drawCrystalShape(size) {
    const facets = 8
    const angleStep = (Math.PI * 2) / facets
    
    for (let i = 0; i < facets; i++) {
      const angle = i * angleStep
      const radius = i % 2 === 0 ? size : size * 0.6
      const x = Math.cos(angle) * radius
      const y = Math.sin(angle) * radius
      
      if (i === 0) {
        this.ctx.moveTo(x, y)
      } else {
        this.ctx.lineTo(x, y)
      }
    }
    this.ctx.closePath()
  }

  /**
   * 绘制像素化星云
   */
  drawPixelNebula(x, y, width, height) {
    const pixelSize = 4
    const cols = Math.floor(width / pixelSize)
    const rows = Math.floor(height / pixelSize)
    
    for (let i = 0; i < cols; i++) {
      for (let j = 0; j < rows; j++) {
        const pixelX = x + i * pixelSize
        const pixelY = y + j * pixelSize
        
        // 使用噪声函数创建星云效果
        const noise = this.noise(i * 0.1, j * 0.1, this.animationTime * 0.0001)
        
        if (noise > 0.3) {
          const intensity = (noise - 0.3) / 0.7
          const alpha = intensity * 0.6
          
          // 根据强度选择颜色
          let color
          if (intensity > 0.8) {
            color = Y2KUIConfig.COLORS.DIGITAL_WHITE
          } else if (intensity > 0.5) {
            color = Y2KUIConfig.COLORS.CYBER_CYAN
          } else {
            color = Y2KUIConfig.COLORS.ELECTRIC_PURPLE
          }
          
          this.ctx.fillStyle = color + Math.floor(alpha * 255).toString(16).padStart(2, '0')
          this.ctx.fillRect(pixelX, pixelY, pixelSize, pixelSize)
        }
      }
    }
  }

  /**
   * 绘制抽象机械部件
   */
  drawAbstractMechanism(x, y, size) {
    this.ctx.save()
    this.ctx.translate(x, y)
    this.ctx.rotate(this.animationTime * 0.001)
    
    this.ctx.strokeStyle = Y2KUIConfig.COLORS.METAL_SILVER
    this.ctx.lineWidth = 1
    
    // 绘制齿轮状结构
    const teeth = 12
    const innerRadius = size * 0.4
    const outerRadius = size * 0.6
    
    this.ctx.beginPath()
    for (let i = 0; i < teeth; i++) {
      const angle = (i * Math.PI * 2) / teeth
      const radius = i % 2 === 0 ? outerRadius : innerRadius
      const x = Math.cos(angle) * radius
      const y = Math.sin(angle) * radius
      
      if (i === 0) {
        this.ctx.moveTo(x, y)
      } else {
        this.ctx.lineTo(x, y)
      }
    }
    this.ctx.closePath()
    this.ctx.stroke()
    
    // 中心圆
    this.ctx.beginPath()
    this.ctx.arc(0, 0, size * 0.2, 0, Math.PI * 2)
    this.ctx.stroke()
    
    this.ctx.restore()
  }

  /**
   * 绘制数据流
   */
  drawDataStreams() {
    this.ctx.font = '12px monospace'
    
    this.dataStreams.forEach(stream => {
      this.ctx.fillStyle = Y2KUIConfig.COLORS.MATRIX_GREEN + 
        Math.floor(stream.opacity * 255).toString(16).padStart(2, '0')
      
      // 绘制字符串
      for (let i = 0; i < stream.chars.length; i++) {
        const charY = stream.y - i * 15
        if (charY > -20 && charY < this.canvas.height + 20) {
          const alpha = Math.max(0, 1 - i / stream.chars.length)
          this.ctx.fillStyle = Y2KUIConfig.COLORS.MATRIX_GREEN + 
            Math.floor(alpha * stream.opacity * 255).toString(16).padStart(2, '0')
          this.ctx.fillText(stream.chars[i], stream.x, charY)
        }
      }
    })
  }

  /**
   * 绘制浮动符号
   */
  drawFloatingSymbols() {
    this.floatingSymbols.forEach(symbol => {
      this.ctx.save()
      this.ctx.translate(symbol.x, symbol.y)
      this.ctx.rotate(symbol.rotation)
      
      this.ctx.fillStyle = Y2KUIConfig.COLORS.CYBER_CYAN + 
        Math.floor(symbol.opacity * 255).toString(16).padStart(2, '0')
      this.ctx.font = `${symbol.size}px monospace`
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      
      this.ctx.fillText(symbol.symbol, 0, 0)
      this.ctx.restore()
    })
  }

  /**
   * 绘制故障效果装饰 - 已禁用
   */
  drawGlitchDecorations() {
    // 移除故障效果以避免视觉疲惫
    return
  }

  /**
   * 简单噪声函数
   */
  noise(x, y, z = 0) {
    return (Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453) % 1
  }

  /**
   * 更新装饰元素
   */
  update(deltaTime) {
    this.animationTime += deltaTime
    
    // 更新浮动符号
    this.floatingSymbols.forEach(symbol => {
      symbol.x += symbol.vx
      symbol.y += symbol.vy
      symbol.rotation += symbol.rotationSpeed
      
      // 边界检查
      if (symbol.x < -50) symbol.x = this.canvas.width + 50
      if (symbol.x > this.canvas.width + 50) symbol.x = -50
      if (symbol.y < -50) symbol.y = this.canvas.height + 50
      if (symbol.y > this.canvas.height + 50) symbol.y = -50
    })
    
    // 更新数据流
    this.dataStreams.forEach(stream => {
      stream.y += stream.speed
      
      if (stream.y > this.canvas.height + stream.length * 15) {
        stream.y = -50
        stream.x = Math.random() * this.canvas.width
        stream.chars = this.generateDataString()
      }
    })
  }

  /**
   * 渲染所有装饰元素
   */
  render() {
    // 绘制背景装饰
    this.drawFloatingSymbols()
    this.drawDataStreams()
    
    // 偶尔绘制故障效果
    this.drawGlitchDecorations()
    
    // 在特定位置绘制几何装饰
    if (Math.random() < 0.3) {
      const x = Math.random() * this.canvas.width
      const y = Math.random() * this.canvas.height
      const size = Math.random() * 30 + 10
      const types = ['triangle', 'hexagon', 'circuit', 'crystal']
      const type = types[Math.floor(Math.random() * types.length)]
      
      this.drawDistortedGeometry(x, y, size, type)
    }
  }

  /**
   * 绘制特定区域的装饰框架
   */
  drawDecorativeFrame(x, y, width, height) {
    const cornerSize = 20
    
    this.ctx.strokeStyle = Y2KUIConfig.COLORS.CYBER_CYAN
    this.ctx.lineWidth = 2
    
    // 绘制角落装饰
    const corners = [
      { x: x, y: y }, // 左上
      { x: x + width, y: y }, // 右上
      { x: x, y: y + height }, // 左下
      { x: x + width, y: y + height } // 右下
    ]
    
    corners.forEach((corner, index) => {
      this.ctx.save()
      this.ctx.translate(corner.x, corner.y)
      this.ctx.rotate(index * Math.PI / 2)
      
      // 绘制L形装饰
      this.ctx.beginPath()
      this.ctx.moveTo(0, 0)
      this.ctx.lineTo(-cornerSize, 0)
      this.ctx.moveTo(0, 0)
      this.ctx.lineTo(0, -cornerSize)
      this.ctx.stroke()
      
      this.ctx.restore()
    })
  }
}
