// 21世纪进化论体系配置
const CHARACTER_CONFIG = [
  // 初始投放角色（游戏开始时随机投放）
  { level: 1, name: '小宝', color: '#FF6B6B', radius: 15, score: 1, isInitial: true, imagePath: 'game_media/images/char_1.png' },
  { level: 2, name: '年轻哥', color: '#FF8E8E', radius: 20, score: 2, isInitial: true, imagePath: 'game_media/images/char_2.png' },
  { level: 3, name: '走读生', color: '#9B59B6', radius: 25, score: 4, isInitial: true, imagePath: 'game_media/images/char_3.png' },
  
  // 进阶合成角色（按等级依次合成）
  { level: 4, name: '大满贯', color: '#F39C12', radius: 30, score: 6, isInitial: false, imagePath: 'game_media/images/char_4.png' },
  { level: 5, name: '研不休', color: '#F1C40F', radius: 35, score: 8, isInitial: false, imagePath: 'game_media/images/char_5.png' },
  { level: 6, name: '博学者', color: '#27AE60', radius: 40, score: 10, isInitial: false, imagePath: 'game_media/images/char_6.png' },
  { level: 7, name: '企小工', color: '#E67E22', radius: 45, score: 100, isInitial: false, imagePath: 'game_media/images/char_7.png' },
  { level: 8, name: '创老板', color: '#8B4513', radius: 50, score: 1000, isInitial: false, imagePath: 'game_media/images/char_8.png' },
  { level: 9, name: '编内人', color: '#E74C3C', radius: 55, score: 1500, isInitial: false, imagePath: 'game_media/images/char_9.png' },
  { level: 10, name: '富小哥', color: '#C0392B', radius: 60, score: 2000, isInitial: false, imagePath: 'game_media/images/char_10.png' },
  
  // 终极角色
  { level: 11, name: '富老炮', color: '#A93226', radius: 65, score: 100000000, isInitial: false, isUltimate: true, imagePath: 'game_media/images/char_11.png' },
  
  // 特殊角色
  { level: 12, name: '丐帮主', color: '#2C3E50', radius: 70, score: 0, isInitial: false, isSpecial: true, imagePath: 'game_media/images/char_12.png' }
]

import { objectPool } from './ObjectPool.js'

export class GameData {
  constructor() {
    this.reset()
  }
  
  reset() {
    this.score = 0
    this.highScore = this.getHighScore()
    this.currentLevel = 1
    this.fruits = []
    this.nextFruit = null
    this.gameOver = false
    this.paused = false
    
    // 游戏统计
    this.gameCount = this.getGameCount()
    this.highScoreTime = this.getHighScoreTime()
    this.gameStartTime = Date.now() // 记录当前游戏开始时间
  }
  
  getFruitConfig(level) {
    return CHARACTER_CONFIG.find(fruit => fruit.level === level) || CHARACTER_CONFIG[0]
  }
  
  getRandomFruitLevel() {
    // 只生成初始投放角色
    const initialCharacters = CHARACTER_CONFIG.filter(char => char.isInitial)
    const randomIndex = Math.floor(Math.random() * initialCharacters.length)
    return initialCharacters[randomIndex].level
  }
  
  generateNextFruit() {
    const level = this.getRandomFruitLevel()
    this.nextFruit = this.getFruitConfig(level)
  }
  
  addFruit(fruit) {
    this.fruits.push(fruit)
  }
  
  removeFruit(fruit) {
    const index = this.fruits.indexOf(fruit)
    if (index > -1) {
      this.fruits.splice(index, 1)
      
      // 如果是从对象池获取的球体，回收到对象池
      if (fruit.isPooled) {
        objectPool.recycleFruit(fruit)
      }
    }
  }
  
  addScore(points) {
    this.score += points
    if (this.score > this.highScore) {
      this.highScore = this.score
      this.saveHighScore()
    }
  }
  
  getHighScore() {
    try {
      return wx.getStorageSync('highScore') || 0
    } catch (e) {
      return 0
    }
  }
  
  saveHighScore() {
    try {
      wx.setStorageSync('highScore', this.highScore)
    } catch (e) {
      console.error('保存最高分失败:', e)
    }
  }
  
  // 游戏次数统计
  getGameCount() {
    try {
      return wx.getStorageSync('gameCount') || 0
    } catch (e) {
      return 0
    }
  }
  
  saveGameCount() {
    try {
      wx.setStorageSync('gameCount', this.gameCount)
    } catch (e) {
      console.error('保存游戏次数失败:', e)
    }
  }
  
  // 最高分游玩时间统计
  getHighScoreTime() {
    try {
      return wx.getStorageSync('highScoreTime') || 0
    } catch (e) {
      return 0
    }
  }
  
  saveHighScoreTime(time) {
    try {
      wx.setStorageSync('highScoreTime', time)
    } catch (e) {
      console.error('保存最高分时间失败:', e)
    }
  }
  
  // 游戏结束时调用，更新统计
  onGameOver() {
    // 增加游戏次数
    this.gameCount++
    this.saveGameCount()
    
    // 如果破纪录，记录游玩时间
    if (this.score >= this.highScore && this.score > 0) {
      const gameTime = Math.floor((Date.now() - this.gameStartTime) / 1000) // 转换为秒
      this.highScoreTime = gameTime
      this.saveHighScoreTime(gameTime)
    }
  }
  
  checkLevelUp() {
    // 每1000分升一级
    const newLevel = Math.floor(this.score / 1000) + 1
    if (newLevel > this.currentLevel && newLevel <= CHARACTER_CONFIG.length) {
      this.currentLevel = newLevel
      return true
    }
    return false
  }
} 