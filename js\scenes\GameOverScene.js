import { Fruit } from '../objects/Fruit.js'
import { Y2KUIConfig } from '../config/Y2KUIConfig.js'
import { PixelEffects } from '../utils/PixelEffects.js'
import { CyberDecorations } from '../utils/CyberDecorations.js'

export class GameOverScene {
  constructor(main) {
    this.main = main
    this.screenWidth = main.screenWidth
    this.screenHeight = main.screenHeight
    this.gameData = main.gameData

    // Y2K UI系统
    this.pixelEffects = new PixelEffects(main.canvas, main.ctx)
    this.cyberDecorations = new CyberDecorations(main.canvas, main.ctx)

    // 动画相关
    this.animationTime = 0
    this.titleGlitchTimer = 0

    // Y2K风格按钮配置
    this.buttons = [
      {
        text: '> 重启系统',
        x: this.screenWidth / 2,
        y: this.screenHeight / 2 + 100,
        width: Y2KUIConfig.DIMENSIONS.BUTTON_PRIMARY.width,
        height: Y2KUIConfig.DIMENSIONS.BUTTON_PRIMARY.height,
        color: Y2KUIConfig.COLORS.MATRIX_GREEN,
        gradient: Y2KUIConfig.COLORS.GRADIENTS.MATRIX_FLOW,
        borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
        isHovered: false,
        pulseAnimation: 1,
        glitchEffect: false
      },
      {
        text: '> 返回主界面',
        x: this.screenWidth / 2,
        y: this.screenHeight / 2 + 180,
        width: Y2KUIConfig.DIMENSIONS.BUTTON_SECONDARY.width + 80,
        height: Y2KUIConfig.DIMENSIONS.BUTTON_SECONDARY.height + 10,
        color: Y2KUIConfig.COLORS.CYBER_CYAN,
        gradient: Y2KUIConfig.COLORS.GRADIENTS.CYBER_BLUE,
        borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
        isHovered: false,
        pulseAnimation: 1,
        glitchEffect: false
      }
    ]

    this.hoveredButton = null

    // Y2K风格音频控制按钮
    this.audioButton = {
      x: 70,
      y: 70,
      width: Y2KUIConfig.DIMENSIONS.BUTTON_ICON.width,
      height: Y2KUIConfig.DIMENSIONS.BUTTON_ICON.height,
      color: Y2KUIConfig.COLORS.MATRIX_GREEN,
      hoverColor: Y2KUIConfig.COLORS.CYBER_CYAN,
      mutedColor: Y2KUIConfig.COLORS.NEON_PINK,
      mutedHoverColor: '#FF0040',
      borderColor: Y2KUIConfig.COLORS.DIGITAL_WHITE,
      isHovered: false,
      pulseAnimation: 1
    }

    // 不再直接绑定事件，由事件管理器统一处理
  }
  
  /**
   * 处理触摸开始事件（由事件管理器调用）
   */
  handleTouchStart(x, y) {
    // 检查音频控制按钮点击
    if (x >= this.audioButton.x - this.audioButton.width / 2 && 
        x <= this.audioButton.x + this.audioButton.width / 2 &&
        y >= this.audioButton.y - this.audioButton.height / 2 && 
        y <= this.audioButton.y + this.audioButton.height / 2) {
      this.main.audioManager.toggleMute()
      
      // 显示状态提示
      const isMuted = this.main.audioManager.isMuted
      wx.showToast({
        title: isMuted ? '音频已关闭' : '音频已开启',
        icon: 'none',
        duration: 1000
      })
      
      return
    }
    
    for (let i = 0; i < this.buttons.length; i++) {
      const button = this.buttons[i]
      if (this.isPointInButton(x, y, button)) {
        this.onButtonClick(i)
        break
      }
    }
  }
  
  /**
   * 处理触摸移动事件（由事件管理器调用）
   */
  handleTouchMove(x, y) {
    // 游戏结束场景不需要处理触摸移动
  }
  
  /**
   * 处理触摸结束事件（由事件管理器调用）
   */
  handleTouchEnd() {
    // 游戏结束场景不需要处理触摸结束
  }
  
  /**
   * 清理事件监听（场景切换时调用）
   */
  cleanupEvents() {
    // 游戏结束场景没有需要清理的事件监听
  }
  
  isPointInButton(x, y, button) {
    return x >= button.x - button.width / 2 &&
           x <= button.x + button.width / 2 &&
           y >= button.y - button.height / 2 &&
           y <= button.y + button.height / 2
  }
  
  onButtonClick(buttonIndex) {
    // 播放点击音效
    this.main.audioManager.playClickSound()
    
    switch (buttonIndex) {
      case 0: // 重新开始
        this.main.switchScene('game')
        break
      case 1: // 返回菜单
        this.main.switchScene('menu')
        break
    }
  }
  
  update() {
    // 更新动画时间
    this.animationTime += 16.67

    // 更新Y2K效果系统
    this.pixelEffects.update(16.67)
    this.cyberDecorations.update(16.67)

    // 更新按钮动画
    this.updateY2KButtonAnimations()

    // 更新标题故障效果
    this.titleGlitchTimer += 16.67
    if (this.titleGlitchTimer > 3000) {
      this.titleGlitchTimer = 0
    }
  }

  /**
   * 更新Y2K风格按钮动画
   */
  updateY2KButtonAnimations() {
    const time = Date.now()

    // 更新按钮脉冲动画
    this.buttons.forEach((button, index) => {
      button.pulseAnimation = 1 + Math.sin(time * 0.005 + index * 0.5) * 0.05

      // 移除随机故障效果
    })

    // 更新音频按钮动画
    this.audioButton.pulseAnimation = 1 + Math.sin(time * 0.008) * 0.05
  }
  
  render(ctx) {
    // 应用像素化设置
    this.pixelEffects.enablePixelation()

    // 绘制Y2K风格背景
    this.renderY2KBackground(ctx)

    // 绘制赛博朋克装饰
    this.cyberDecorations.render()

    // 绘制Y2K风格游戏结束面板
    this.renderY2KGameOverPanel(ctx)

    // 绘制Y2K风格音频控制按钮
    this.renderY2KAudioButton(ctx)

    // 绘制Y2K风格按钮
    this.renderY2KButtons(ctx)

    // 绘制像素效果
    this.pixelEffects.render()
  }

  /**
   * 绘制Y2K风格背景
   */
  renderY2KBackground(ctx) {
    // 基础黑色背景
    ctx.fillStyle = Y2KUIConfig.COLORS.PIXEL_BLACK
    ctx.fillRect(0, 0, this.screenWidth, this.screenHeight)

    // 绘制数字雨背景效果
    this.pixelEffects.drawDigitalRain(
      0, 0, this.screenWidth, this.screenHeight
    )

    // 移除故障效果
  }

  /**
   * 绘制Y2K风格游戏结束面板
   */
  renderY2KGameOverPanel(ctx) {
    const panelWidth = Y2KUIConfig.DIMENSIONS.MODAL_PANEL.width
    const panelHeight = Y2KUIConfig.DIMENSIONS.MODAL_PANEL.height
    const panelX = (this.screenWidth - panelWidth) / 2
    const panelY = (this.screenHeight - panelHeight) / 2

    // 绘制玻璃拟态面板背景
    const panelConfig = {
      color: Y2KUIConfig.COLORS.ALPHA.BACKGROUND_OVERLAY,
      borderColor: Y2KUIConfig.COLORS.NEON_PINK,
      isHovered: false,
      glassStyle: true // 启用玻璃拟态风格
    }

    this.pixelEffects.drawPixelButton(
      this.screenWidth / 2, this.screenHeight / 2,
      panelWidth, panelHeight,
      panelConfig
    )

    // 绘制像素化边框
    this.pixelEffects.drawPixelBorder(
      panelX, panelY, panelWidth, panelHeight,
      Y2KUIConfig.COLORS.NEON_PINK,
      Y2KUIConfig.DIMENSIONS.PIXEL_BORDER
    )

    // 绘制装饰框架
    this.cyberDecorations.drawDecorativeFrame(
      panelX - 15, panelY - 15,
      panelWidth + 30, panelHeight + 30
    )

    // 移除故障效果，只使用正常文字渲染
    const titleText = '< SYSTEM TERMINATED >'
    this.pixelEffects.drawPixelText(
      titleText,
      this.screenWidth / 2, panelY + 60,
      { ...Y2KUIConfig.FONTS.PIXEL_TITLE, size: Math.min(Y2KUIConfig.FONTS.PIXEL_TITLE.size, 32) },
      Y2KUIConfig.COLORS.NEON_PINK,
      Y2KUIConfig.COLORS.PIXEL_BLACK
    )

    // 绘制得分信息
    const centerX = this.screenWidth / 2
    let currentY = panelY + 120

    // 本局得分
    const scoreText = `FINAL_SCORE: ${this.gameData.score.toString().padStart(6, '0')}`
    this.pixelEffects.drawPixelText(
      scoreText,
      centerX, currentY,
      Y2KUIConfig.FONTS.DIGITAL_SCORE,
      Y2KUIConfig.COLORS.CYBER_CYAN,
      Y2KUIConfig.COLORS.PIXEL_BLACK
    )
    currentY += 40

    // 最高分
    const highScoreText = `HIGH_SCORE: ${this.gameData.highScore.toString().padStart(6, '0')}`
    this.pixelEffects.drawPixelText(
      highScoreText,
      centerX, currentY,
      { ...Y2KUIConfig.FONTS.DIGITAL_SCORE, size: 20 },
      Y2KUIConfig.COLORS.MATRIX_GREEN,
      Y2KUIConfig.COLORS.PIXEL_BLACK
    )
    currentY += 40

    // 达到等级
    const levelText = `MAX_LEVEL: ${this.gameData.currentLevel.toString().padStart(2, '0')}`
    this.pixelEffects.drawPixelText(
      levelText,
      centerX, currentY,
      { ...Y2KUIConfig.FONTS.DIGITAL_SCORE, size: 20 },
      Y2KUIConfig.COLORS.ELECTRIC_PURPLE,
      Y2KUIConfig.COLORS.PIXEL_BLACK
    )
    currentY += 50

    // 显示是否破纪录
    if (this.gameData.score >= this.gameData.highScore && this.gameData.score > 0) {
      const recordText = '< NEW RECORD ACHIEVED >'
      this.pixelEffects.drawPixelText(
        recordText,
        centerX, currentY,
        Y2KUIConfig.FONTS.GLITCH_TEXT,
        Y2KUIConfig.COLORS.LIQUID_ORANGE,
        Y2KUIConfig.COLORS.PIXEL_BLACK
      )
    }
  }
  
  /**
   * 绘制Y2K风格音频按钮
   */
  renderY2KAudioButton(ctx) {
    const button = this.audioButton
    const isMuted = this.main.audioManager.isMuted

    // 按钮配置
    const buttonConfig = {
      color: isMuted ? button.mutedColor : button.color,
      borderColor: button.borderColor,
      isHovered: button.isHovered,
      glassStyle: true // 启用玻璃拟态风格
    }

    // 绘制按钮
    this.pixelEffects.drawPixelButton(
      button.x, button.y,
      button.width, button.height,
      buttonConfig
    )

    // 绘制音频图标
    const iconSymbol = isMuted ? '✕' : '♪'
    this.pixelEffects.drawPixelText(
      iconSymbol,
      button.x, button.y,
      Y2KUIConfig.FONTS.PIXEL_BUTTON,
      Y2KUIConfig.COLORS.DIGITAL_WHITE,
      Y2KUIConfig.COLORS.PIXEL_BLACK
    )
  }

  /**
   * 绘制Y2K风格按钮
   */
  renderY2KButtons(ctx) {
    this.buttons.forEach((button, index) => {
      const isHovered = this.hoveredButton === index
      button.isHovered = isHovered

      // 按钮配置
      const buttonConfig = {
        color: button.color,
        gradient: button.gradient,
        borderColor: button.borderColor,
        isHovered: isHovered,
        glitchEffect: button.glitchEffect,
        glassStyle: true // 启用玻璃拟态风格
      }

      // 计算按钮缩放
      const scale = button.pulseAnimation
      const scaledWidth = button.width * scale
      const scaledHeight = button.height * scale

      // 绘制按钮
      this.pixelEffects.drawPixelButton(
        button.x, button.y,
        scaledWidth, scaledHeight,
        buttonConfig
      )

      // 绘制按钮文字
      const fontConfig = Y2KUIConfig.FONTS.PIXEL_BUTTON

      // 移除故障效果，只使用正常文字渲染
      this.pixelEffects.drawPixelText(
        button.text,
        button.x, button.y,
        fontConfig,
        Y2KUIConfig.COLORS.DIGITAL_WHITE,
        Y2KUIConfig.COLORS.PIXEL_BLACK
      )

      // 悬停装饰效果
      if (isHovered) {
        this.cyberDecorations.drawDecorativeFrame(
          button.x - scaledWidth / 2 - 10,
          button.y - scaledHeight / 2 - 10,
          scaledWidth + 20,
          scaledHeight + 20
        )
      }
    })
  }
}