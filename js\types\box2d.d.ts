// Box2D WASM TypeScript 声明文件
// 为 IDE 提供类型支持和代码提示

declare module 'box2d-wasm' {
  export interface Box2DModule {
    b2World: typeof b2World
    b2Body: typeof b2Body
    b2Fixture: typeof b2Fixture
    b2CircleShape: typeof b2CircleShape
    b2Vec2: typeof b2Vec2
    b2BodyDef: typeof b2BodyDef
    b2FixtureDef: typeof b2FixtureDef
    JSContactListener: typeof JSContactListener
  }

  export interface b2World {
    new(gravity: b2Vec2): b2World
    Step(timeStep: number, velocityIterations: number, positionIterations: number): void
    ClearForces(): void
    CreateBody(bodyDef: b2BodyDef): b2Body
    DestroyBody(body: b2Body): void
    SetContactListener(listener: JSContactListener): void
  }

  export interface b2Body {
    b2_staticBody: number
    b2_dynamicBody: number
    b2_kinematicBody: number
    SetPosition(position: b2Vec2): void
    GetPosition(): b2Vec2
    SetType(type: number): void
    GetType(): number
    ApplyForce(force: b2Vec2, point: b2Vec2): void
    GetLinearVelocity(): b2Vec2
    CreateFixture(fixtureDef: b2FixtureDef): b2Fixture
  }

  export interface b2Fixture {
    GetBody(): b2Body
    GetFixtureA(): b2Fixture
    GetFixtureB(): b2Fixture
  }

  export interface b2CircleShape {
    new(): b2CircleShape
    m_radius: number
  }

  export interface b2Vec2 {
    new(x: number, y: number): b2Vec2
    x: number
    y: number
    Set(x: number, y: number): void
    Length(): number
  }

  export interface b2BodyDef {
    new(): b2BodyDef
    type: number
    position: b2Vec2
    linearDamping: number
    angularDamping: number
  }

  export interface b2FixtureDef {
    new(): b2FixtureDef
    shape: b2CircleShape
    density: number
    friction: number
    restitution: number
    userData: any
  }

  export interface JSContactListener {
    new(): JSContactListener
    BeginContact(contact: b2Contact): void
    EndContact(contact: b2Contact): void
  }

  export interface b2Contact {
    GetFixtureA(): b2Fixture
    GetFixtureB(): b2Fixture
  }

  export default function Box2D(options: {
    wasmBinary: ArrayBuffer
    onRuntimeInitialized?: () => void
  }): Promise<Box2DModule>
}

// 游戏相关类型声明
declare global {
  interface Fruit {
    level: number
    name: string
    color: string
    radius: number
    score: number
    x: number
    y: number
    vx: number
    vy: number
    gravity: number
    friction: number
    bounce: number
    body: any
    isStatic: boolean
    isDragging: boolean
    dragStartX: number
    dragStartY: number
    isMarkedForMerge: boolean
    collisionRadius: number
    createBody(): void
    update(): void
    checkBoundaryCollision(): void
    destroy(): void
    checkCollision(other: Fruit): boolean
    handleCollision(other: Fruit): boolean
    startDrag(x: number, y: number): void
    updateDrag(x: number, y: number): void
    endDrag(): void
    render(ctx: CanvasRenderingContext2D): void
  }

  interface Box2DLoader {
    isLoaded: boolean
    world: any
    bodies: Map<any, any>
    contactListener: any
    useSimplePhysics: boolean
    load(): Promise<void>
    createFruitBody(fruit: Fruit): any
    removeFruitBody(fruit: Fruit): void
    step(timeStep?: number, velocityIterations?: number, positionIterations?: number): void
    setFruitPosition(fruit: Fruit, x: number, y: number): void
    applyForceToFruit(fruit: Fruit, forceX: number, forceY: number): void
    setMergeCallback(callback: (fruitA: Fruit, fruitB: Fruit) => void): void
    onFruitMerge(fruitA: Fruit, fruitB: Fruit): void
    destroy(): void
  }
} 