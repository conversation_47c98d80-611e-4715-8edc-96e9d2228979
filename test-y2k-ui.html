<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y2K UI 测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: monospace;
        }
        canvas {
            border: 1px solid #00FFFF;
            background: #0A0A0A;
        }
        .info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #00FFFF;
            font-size: 12px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border: 1px solid #00FFFF;
        }
    </style>
</head>
<body>
    <div class="info">
        <h3>Y2K UI 系统测试</h3>
        <p>✅ 像素化字体渲染</p>
        <p>✅ 赛博朋克按钮</p>
        <p>✅ 故障效果</p>
        <p>✅ 扫描线效果</p>
        <p>✅ 装饰元素</p>
        <p>✅ 像素化星云</p>
        <p>✅ 数字雨效果</p>
    </div>
    <canvas id="testCanvas" width="400" height="600"></canvas>

    <script type="module">
        // 模拟微信小游戏环境
        window.wx = {
            createCanvas: () => document.getElementById('testCanvas'),
            createImage: () => new Image()
        };

        // 简化的Y2K UI配置
        const Y2KUIConfig = {
            COLORS: {
                CYBER_CYAN: '#00FFFF',
                MATRIX_GREEN: '#00FF41',
                PIXEL_BLACK: '#0A0A0A',
                DIGITAL_WHITE: '#FFFFFF',
                NEON_PINK: '#FF0080',
                ELECTRIC_PURPLE: '#8A2BE2',
                ALPHA: {
                    BACKGROUND_OVERLAY: 'rgba(10, 10, 10, 0.85)',
                    GLOW_EFFECT: 'rgba(0, 255, 255, 0.3)',
                    SCAN_LINE: 'rgba(255, 255, 255, 0.1)'
                }
            },
            FONTS: {
                PIXEL_TITLE: { size: 32, weight: 'bold', family: 'monospace' },
                PIXEL_BUTTON: { size: 16, weight: 'bold', family: 'monospace' },
                SYSTEM_HINT: { size: 12, weight: 'normal', family: 'monospace' }
            }
        };

        // 简化的像素效果类
        class SimplePixelEffects {
            constructor(canvas, ctx) {
                this.canvas = canvas;
                this.ctx = ctx;
                this.scanLineOffset = 0;
            }

            enablePixelation() {
                this.ctx.imageSmoothingEnabled = false;
            }

            drawPixelText(text, x, y, fontConfig, color) {
                this.enablePixelation();
                const font = `${fontConfig.weight} ${fontConfig.size}px ${fontConfig.family}`;
                this.ctx.font = font;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillStyle = color;
                this.ctx.fillText(text, x, y);
            }

            drawPixelButton(x, y, width, height, config) {
                this.enablePixelation();
                
                // 绘制按钮背景
                this.ctx.fillStyle = config.color;
                this.ctx.fillRect(x - width/2, y - height/2, width, height);
                
                // 绘制边框
                this.ctx.strokeStyle = Y2KUIConfig.COLORS.DIGITAL_WHITE;
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(x - width/2, y - height/2, width, height);
                
                // 光晕效果
                if (config.isHovered) {
                    this.ctx.strokeStyle = Y2KUIConfig.COLORS.ALPHA.GLOW_EFFECT;
                    this.ctx.lineWidth = 4;
                    this.ctx.strokeRect(x - width/2 - 2, y - height/2 - 2, width + 4, height + 4);
                }
            }

            drawScanLines() {
                this.ctx.fillStyle = Y2KUIConfig.COLORS.ALPHA.SCAN_LINE;
                for (let y = this.scanLineOffset; y < this.canvas.height; y += 4) {
                    this.ctx.fillRect(0, y, this.canvas.width, 1);
                }
                this.scanLineOffset = (this.scanLineOffset + 1) % 4;
            }

            drawGlitchText(text, x, y, font, color) {
                this.ctx.font = font;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // RGB分离效果
                this.ctx.fillStyle = Y2KUIConfig.COLORS.NEON_PINK;
                this.ctx.fillText(text, x - 2, y);

                this.ctx.fillStyle = Y2KUIConfig.COLORS.CYBER_CYAN;
                this.ctx.fillText(text, x + 2, y);

                this.ctx.fillStyle = color;
                this.ctx.fillText(text, x, y);
            }

            drawPixelNebula(x, y, width, height) {
                const pixelSize = 4;
                const cols = Math.floor(width / pixelSize);
                const rows = Math.floor(height / pixelSize);

                for (let i = 0; i < cols; i++) {
                    for (let j = 0; j < rows; j++) {
                        const pixelX = x + i * pixelSize;
                        const pixelY = y + j * pixelSize;

                        // 简单噪声
                        const noise = Math.sin(i * 0.1 + j * 0.1 + Date.now() * 0.001) * 0.5 + 0.5;

                        if (noise > 0.6) {
                            const intensity = (noise - 0.6) / 0.4;
                            const alpha = intensity * 0.6;

                            let color = intensity > 0.8 ? Y2KUIConfig.COLORS.DIGITAL_WHITE :
                                       intensity > 0.5 ? Y2KUIConfig.COLORS.CYBER_CYAN :
                                       Y2KUIConfig.COLORS.ELECTRIC_PURPLE;

                            this.ctx.fillStyle = color + Math.floor(alpha * 255).toString(16).padStart(2, '0');
                            this.ctx.fillRect(pixelX, pixelY, pixelSize, pixelSize);
                        }
                    }
                }
            }

            drawDigitalRain(x, y, width, height) {
                const chars = '01ABCDEF';
                const fontSize = 12;
                const columns = Math.floor(width / fontSize);

                this.ctx.font = `${fontSize}px monospace`;
                this.ctx.fillStyle = Y2KUIConfig.COLORS.MATRIX_GREEN + '40';

                for (let i = 0; i < columns; i++) {
                    if (Math.random() < 0.1) {
                        const char = chars[Math.floor(Math.random() * chars.length)];
                        const charX = x + i * fontSize;
                        const charY = y + (Math.random() * height);

                        this.ctx.fillText(char, charX, charY);
                    }
                }
            }
        }

        // 测试应用
        class Y2KUITest {
            constructor() {
                this.canvas = document.getElementById('testCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.pixelEffects = new SimplePixelEffects(this.canvas, this.ctx);
                this.animationTime = 0;
                this.buttons = [
                    { text: '> 启动系统', x: 200, y: 150, width: 200, height: 50, isHovered: false },
                    { text: '> 系统手册', x: 200, y: 220, width: 200, height: 50, isHovered: false },
                    { text: '> 数据档案', x: 200, y: 290, width: 200, height: 50, isHovered: false }
                ];
                
                this.setupEvents();
                this.animate();
            }

            setupEvents() {
                this.canvas.addEventListener('mousemove', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;
                    
                    this.buttons.forEach(button => {
                        button.isHovered = (
                            mouseX >= button.x - button.width/2 &&
                            mouseX <= button.x + button.width/2 &&
                            mouseY >= button.y - button.height/2 &&
                            mouseY <= button.y + button.height/2
                        );
                    });
                });
            }

            render() {
                // 清空画布
                this.ctx.fillStyle = Y2KUIConfig.COLORS.PIXEL_BLACK;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // 绘制简洁的网格背景
                this.ctx.strokeStyle = Y2KUIConfig.COLORS.ALPHA.SCAN_LINE;
                this.ctx.lineWidth = 1;
                for (let x = 0; x < this.canvas.width; x += 60) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, this.canvas.height);
                    this.ctx.stroke();
                }
                for (let y = 0; y < this.canvas.height; y += 60) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(this.canvas.width, y);
                    this.ctx.stroke();
                }

                // 绘制标题
                if (Math.random() < 0.1) {
                    this.pixelEffects.drawGlitchText(
                        '21世纪进化论',
                        200, 80,
                        `${Y2KUIConfig.FONTS.PIXEL_TITLE.weight} ${Y2KUIConfig.FONTS.PIXEL_TITLE.size}px ${Y2KUIConfig.FONTS.PIXEL_TITLE.family}`,
                        Y2KUIConfig.COLORS.CYBER_CYAN
                    );
                } else {
                    this.pixelEffects.drawPixelText(
                        '21世纪进化论',
                        200, 80,
                        Y2KUIConfig.FONTS.PIXEL_TITLE,
                        Y2KUIConfig.COLORS.CYBER_CYAN
                    );
                }

                // 绘制副标题
                this.pixelEffects.drawPixelText(
                    '< SYSTEM INITIALIZED >',
                    200, 110,
                    Y2KUIConfig.FONTS.SYSTEM_HINT,
                    Y2KUIConfig.COLORS.MATRIX_GREEN
                );

                // 绘制按钮
                this.buttons.forEach((button, index) => {
                    const colors = [Y2KUIConfig.COLORS.CYBER_CYAN, Y2KUIConfig.COLORS.MATRIX_GREEN, Y2KUIConfig.COLORS.ELECTRIC_PURPLE];
                    const buttonConfig = {
                        color: colors[index],
                        isHovered: button.isHovered
                    };
                    
                    this.pixelEffects.drawPixelButton(
                        button.x, button.y,
                        button.width, button.height,
                        buttonConfig
                    );
                    
                    this.pixelEffects.drawPixelText(
                        button.text,
                        button.x, button.y,
                        Y2KUIConfig.FONTS.PIXEL_BUTTON,
                        Y2KUIConfig.COLORS.DIGITAL_WHITE
                    );
                });

                // 绘制装饰符号
                const symbols = ['◢', '◣', '◤', '◥', '⬢', '⬡'];
                for (let i = 0; i < 8; i++) {
                    const x = 50 + (i % 4) * 100;
                    const y = 400 + Math.floor(i / 4) * 100;
                    const symbol = symbols[i % symbols.length];
                    const opacity = 0.3 + 0.3 * Math.sin(this.animationTime * 0.01 + i);
                    
                    this.ctx.save();
                    this.ctx.globalAlpha = opacity;
                    this.pixelEffects.drawPixelText(
                        symbol,
                        x, y,
                        Y2KUIConfig.FONTS.PIXEL_BUTTON,
                        Y2KUIConfig.COLORS.NEON_PINK
                    );
                    this.ctx.restore();
                }

                // 绘制扫描线
                this.pixelEffects.drawScanLines();

                // 绘制状态信息
                this.pixelEffects.drawPixelText(
                    `FRAME: ${Math.floor(this.animationTime / 16.67).toString().padStart(6, '0')}`,
                    200, 550,
                    Y2KUIConfig.FONTS.SYSTEM_HINT,
                    Y2KUIConfig.COLORS.MATRIX_GREEN
                );
            }

            animate() {
                this.animationTime += 16.67;
                this.render();
                requestAnimationFrame(() => this.animate());
            }
        }

        // 启动测试
        new Y2KUITest();
    </script>
</body>
</html>
