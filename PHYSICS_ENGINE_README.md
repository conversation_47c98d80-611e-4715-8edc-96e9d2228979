# 微信物理引擎集成说明

## 概述

本项目已成功集成微信官方物理引擎，解决了图片替换后角色无法正常下落的问题。新的物理引擎系统提供了以下特性：

- **微信官方物理引擎**：基于 `wx.createPhysicsWorld()` 的高精度物理模拟
- **简化物理引擎备用方案**：当微信物理引擎不可用时自动降级
- **统一物理引擎管理器**：确保所有角色使用同一个物理引擎实例
- **完全兼容现有代码**：无需修改游戏逻辑，自动适配

## 文件结构

```
js/libs/
├── wechat-physics.js          # 微信物理引擎适配器
├── simple-physics-fallback.js # 简化物理引擎备用方案
└── physics-manager.js         # 物理引擎管理器

js/objects/
└── Fruit.js                   # 角色类（已更新）

js/scenes/
└── GameScene.js               # 游戏场景（已更新）

js/test/
├── PhysicsTest.js             # 基础物理测试
└── WeChatPhysicsTest.js       # 微信物理引擎测试
```

## 核心功能

### 1. 微信物理引擎适配器 (`wechat-physics.js`)

- 基于微信小游戏官方 API `wx.createPhysicsWorld()`
- 提供与现有 Box2D 代码兼容的接口
- 支持圆形碰撞体、重力、摩擦力、弹性等物理属性
- 自动处理物理单位转换（游戏像素 ↔ 物理单位）

### 2. 简化物理引擎备用方案 (`simple-physics-fallback.js`)

- 当微信物理引擎不可用时自动启用
- 提供基本的重力、碰撞、摩擦力模拟
- 确保游戏在任何环境下都能正常运行

### 3. 物理引擎管理器 (`physics-manager.js`)

- 统一管理物理引擎实例
- 确保所有角色使用同一个物理引擎
- 提供状态监控和错误处理
- 支持引擎重置和重新初始化

## 使用方法

### 基本使用

```javascript
import { physicsManager } from './js/libs/physics-manager.js'

// 初始化物理引擎
const physicsEngine = await physicsManager.init()

// 获取物理引擎状态
const status = physicsManager.getStatus()
console.log(`物理引擎类型: ${status.engineType}`) // 'wechat' 或 'simple'
```

### 在角色中使用

```javascript
import { physicsManager } from '../libs/physics-manager.js'

class Fruit {
  async createBody() {
    try {
      const physicsEngine = physicsManager.getPhysicsEngine()
      if (physicsEngine && physicsEngine.createFruitBody) {
        this.body = physicsEngine.createFruitBody(this, this.x, this.y)
        const status = physicsManager.getStatus()
        console.log(`角色 ${this.name} ${status.engineType}物理刚体创建成功`)
      }
    } catch (error) {
      console.warn(`物理刚体创建失败:`, error)
    }
  }
}
```

### 在游戏场景中使用

```javascript
import { physicsManager } from '../libs/physics-manager.js'

class GameScene {
  async start() {
    // 初始化物理引擎
    this.physicsEngine = await physicsManager.init()
    const status = physicsManager.getStatus()
    console.log(`物理引擎初始化成功，类型: ${status.engineType}`)
  }

  update() {
    // 使用物理引擎更新
    if (this.physicsEngine && typeof this.physicsEngine.step === 'function') {
      this.physicsEngine.step()
    }
  }
}
```

## 物理属性配置

### 微信物理引擎参数

```javascript
// 刚体定义
const bodyDef = {
  type: 'dynamic',           // 动态刚体
  position: { x: 0, y: 0 },  // 位置
  linearDamping: 0.05,       // 线性阻尼
  angularDamping: 0.05,      // 角阻尼
  allowSleep: true,          // 允许休眠
  awake: true,               // 唤醒状态
  bullet: false              // 子弹模式
}

// 碰撞体定义
const fixtureDef = {
  shape: {
    type: 'circle',          // 圆形
    radius: 1.0              // 半径
  },
  density: 1.0,              // 密度
  friction: 0.2,             // 摩擦力
  restitution: 0.1,          // 弹性
  isSensor: false            // 传感器模式
}
```

### 简化物理引擎参数

```javascript
// 物理参数
const physicsParams = {
  gravity: { x: 0, y: 9.8 },  // 重力
  timeStep: 1/60,              // 时间步长
  damping: 0.98,               // 阻尼系数
  mass: radius / 10            // 质量（基于半径）
}
```

## 测试和调试

### 运行物理引擎测试

```javascript
import { weChatPhysicsTest } from './js/test/WeChatPhysicsTest.js'

// 运行基础测试
const result = await weChatPhysicsTest.runTest()
console.log('测试结果:', result)

// 运行多角色测试
await weChatPhysicsTest.testMultipleFruits()

// 清理测试
weChatPhysicsTest.cleanup()
```

### 调试信息

物理引擎会输出详细的调试信息：

```
物理引擎管理器：微信物理引擎初始化成功
角色 小宝 微信物理刚体创建成功
物理步进: 重力应用成功
```

### 状态监控

```javascript
const status = physicsManager.getStatus()
console.log('物理引擎状态:', {
  isInitialized: status.isInitialized,  // 是否已初始化
  engineType: status.engineType,        // 引擎类型
  bodyCount: status.bodyCount,          // 刚体数量
  gravity: status.gravity               // 重力设置
})
```

## 故障排除

### 常见问题

1. **微信物理引擎初始化失败**
   - 检查微信开发者工具版本是否支持物理引擎
   - 确认项目配置中启用了物理引擎功能
   - 查看控制台错误信息

2. **角色不下落**
   - 确认物理引擎已正确初始化
   - 检查角色刚体是否成功创建
   - 验证物理步进是否被正确调用

3. **性能问题**
   - 减少同时存在的角色数量
   - 调整物理步进频率
   - 使用简化物理引擎作为备用方案

### 错误处理

```javascript
try {
  const physicsEngine = await physicsManager.init()
} catch (error) {
  console.error('物理引擎初始化失败:', error)
  // 自动降级到简化物理引擎
}
```

## 性能优化建议

1. **合理设置物理参数**
   - 调整重力大小以适应游戏需求
   - 设置合适的阻尼系数减少计算量
   - 启用刚体休眠功能

2. **优化碰撞检测**
   - 使用适当的碰撞体形状
   - 避免过多的传感器对象
   - 定期清理无用的刚体

3. **监控性能**
   - 定期检查物理引擎状态
   - 监控刚体数量变化
   - 根据设备性能调整物理精度

## 更新日志

### v1.0.0 (当前版本)
- ✅ 集成微信官方物理引擎
- ✅ 实现简化物理引擎备用方案
- ✅ 创建统一物理引擎管理器
- ✅ 修复图片替换后角色不下落的问题
- ✅ 添加完整的测试和调试功能
- ✅ 提供详细的文档和使用说明

## 技术支持

如果在使用过程中遇到问题，请：

1. 查看控制台错误信息
2. 运行物理引擎测试
3. 检查微信开发者工具版本
4. 参考微信小游戏官方文档

---

**注意**：本物理引擎系统专为微信小游戏环境设计，确保在微信开发者工具和真机环境中都能正常运行。 