/**
 * 像素化效果系统
 * 实现90年代低分辨率屏幕的视觉特征
 */

import { Y2KUIConfig, getResponsiveScale, getResponsiveFontSize, getDeviceSpecificConfig } from '../config/Y2KUIConfig.js'

export class PixelEffects {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.scanLineOffset = 0
    this.glitchTimer = 0
    this.animationTime = 0
    this.noisePattern = this.generateNoisePattern()

    // 响应式配置
    this.deviceConfig = getDeviceSpecificConfig(canvas.width)
    this.responsiveScale = getResponsiveScale(canvas.width)

    // CRT效果参数
    this.crtParams = {
      scanLineOpacity: 0.1,
      vignetteStrength: 0.3,
      phosphorGlow: true,
      curvature: 0.1
    }
  }

  /**
   * 生成噪点图案
   */
  generateNoisePattern() {
    const size = 64
    const pattern = new Array(size * size)
    for (let i = 0; i < pattern.length; i++) {
      pattern[i] = Math.random() > 0.5 ? 1 : 0
    }
    return pattern
  }

  /**
   * 应用像素化渲染设置
   */
  enablePixelation() {
    this.ctx.imageSmoothingEnabled = false
    this.ctx.webkitImageSmoothingEnabled = false
    this.ctx.mozImageSmoothingEnabled = false
    this.ctx.msImageSmoothingEnabled = false
  }

  /**
   * 绘制像素化文字（响应式）
   */
  drawPixelText(text, x, y, fontConfig, color, strokeColor = null, isGlassStyle = false, isHovered = false) {
    // 响应式字体大小
    const responsiveFontSize = getResponsiveFontSize(fontConfig.size, this.canvas.width)

    // 根据风格选择字体
    let fontFamily = fontConfig.family
    if (isGlassStyle) {
      fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }

    const font = `${fontConfig.weight} ${responsiveFontSize}px ${fontFamily}`

    this.ctx.font = font
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'

    if (isGlassStyle) {
      // 玻璃风格文字渲染
      this.drawGlassText(text, x, y, color, isHovered)
    } else {
      // 原有像素风格文字渲染
      this.enablePixelation()

      // 响应式描边宽度
      const strokeWidth = Math.max(1, Math.floor((fontConfig.strokeWidth || 1) * this.responsiveScale))

      // 绘制描边
      if (strokeColor && strokeWidth > 0) {
        this.ctx.strokeStyle = strokeColor
        this.ctx.lineWidth = strokeWidth
        this.ctx.strokeText(text, x, y)
      }

      // 绘制主文字
      this.ctx.fillStyle = color
      this.ctx.fillText(text, x, y)

      // 故障效果
      if (fontConfig.glitchEffect && Math.random() < 0.1) {
        this.drawGlitchText(text, x, y, font, color)
      }
    }
  }

  /**
   * 绘制玻璃风格文字
   */
  drawGlassText(text, x, y, color, isHovered) {
    this.ctx.save()

    // 文字阴影
    this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
    this.ctx.shadowBlur = 4
    this.ctx.shadowOffsetX = 0
    this.ctx.shadowOffsetY = 2

    // 主文字颜色
    this.ctx.fillStyle = color || 'rgba(255, 255, 255, 0.9)'
    this.ctx.fillText(text, x, y)

    this.ctx.restore()

    // 悬停时的微光效果
    if (isHovered && Math.random() < 0.3) {
      this.ctx.save()
      this.ctx.shadowColor = 'rgba(255, 255, 255, 0.8)'
      this.ctx.shadowBlur = 8
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)'
      this.ctx.fillText(text, x, y)
      this.ctx.restore()
    }
  }

  /**
   * 绘制故障文字效果
   */
  drawGlitchText(text, x, y, font, baseColor) {
    this.ctx.font = font
    
    // RGB分离效果
    const offset = 2
    
    // 红色通道
    this.ctx.fillStyle = Y2KUIConfig.COLORS.NEON_PINK
    this.ctx.fillText(text, x - offset, y)
    
    // 蓝色通道
    this.ctx.fillStyle = Y2KUIConfig.COLORS.CYBER_CYAN
    this.ctx.fillText(text, x + offset, y)
    
    // 主文字（绿色通道）
    this.ctx.fillStyle = baseColor
    this.ctx.fillText(text, x, y)
  }

  /**
   * 绘制按钮（支持多种风格）
   */
  drawPixelButton(x, y, width, height, config) {
    // 响应式尺寸调整
    const responsiveWidth = Math.max(Y2KUIConfig.RESPONSIVE.MIN_BUTTON_SIZE, width * this.responsiveScale)
    const responsiveHeight = Math.max(Y2KUIConfig.RESPONSIVE.MIN_BUTTON_SIZE, height * this.responsiveScale)

    // 计算中心点
    const centerX = x
    const centerY = y

    if (config.glassStyle) {
      // 玻璃拟态风格
      const borderRadius = responsiveHeight * 0.5 // 完全圆角

      // 绘制外层阴影（更大、更柔和）
      this.drawGlassShadow(centerX, centerY, responsiveWidth, responsiveHeight, borderRadius, config.isHovered)

      // 绘制玻璃按钮主体
      this.drawGlassButton(centerX, centerY, responsiveWidth, responsiveHeight, borderRadius, config)

      // 绘制高光效果
      this.drawGlassHighlight(centerX, centerY, responsiveWidth, responsiveHeight, borderRadius, config.isHovered)
    } else {
      // 原有像素风格
      this.drawPixelStyleButton(centerX, centerY, responsiveWidth, responsiveHeight, config)
    }
  }

  /**
   * 绘制像素风格按钮（原有风格）
   */
  drawPixelStyleButton(x, y, width, height, config) {
    this.enablePixelation()

    const halfWidth = width / 2
    const halfHeight = height / 2

    // 响应式阴影偏移
    const shadowOffset = Math.max(2, Math.floor(3 * this.responsiveScale))

    // 绘制阴影
    this.ctx.fillStyle = Y2KUIConfig.COLORS.ALPHA.SHADOW
    this.ctx.fillRect(
      x - halfWidth + shadowOffset,
      y - halfHeight + shadowOffset,
      width,
      height
    )

    // 绘制按钮背景渐变
    const gradient = this.ctx.createLinearGradient(
      x - halfWidth,
      y - halfHeight,
      x + halfWidth,
      y + halfHeight
    )

    if (config.gradient) {
      config.gradient.forEach((color, index) => {
        gradient.addColorStop(index / (config.gradient.length - 1), color)
      })
    } else {
      gradient.addColorStop(0, config.color)
      gradient.addColorStop(1, this.darkenColor(config.color, 0.3))
    }

    this.ctx.fillStyle = gradient
    this.ctx.fillRect(
      x - halfWidth,
      y - halfHeight,
      width,
      height
    )

    // 响应式边框宽度
    const borderWidth = Math.max(1, Math.floor(this.deviceConfig.borderWidth))

    // 绘制像素化边框
    this.drawPixelBorder(
      x - halfWidth,
      y - halfHeight,
      width,
      height,
      config.borderColor || Y2KUIConfig.COLORS.DIGITAL_WHITE,
      borderWidth
    )

    // 悬停光晕效果
    if (config.isHovered) {
      this.drawGlowEffect(x, y, width, height)
    }
  }

  /**
   * 绘制玻璃按钮阴影
   */
  drawGlassShadow(x, y, width, height, radius, isHovered) {
    const shadowOffset = isHovered ? 8 : 6
    const shadowBlur = isHovered ? 20 : 15

    // 创建阴影路径
    this.ctx.save()
    this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
    this.ctx.shadowBlur = shadowBlur
    this.ctx.shadowOffsetX = 0
    this.ctx.shadowOffsetY = shadowOffset

    // 绘制圆角矩形阴影
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    this.drawRoundedRect(x - width/2, y - height/2, width, height, radius)
    this.ctx.fill()

    this.ctx.restore()
  }

  /**
   * 绘制玻璃按钮主体
   */
  drawGlassButton(x, y, width, height, radius, config) {
    this.ctx.save()

    // 创建按钮路径
    this.drawRoundedRect(x - width/2, y - height/2, width, height, radius)

    // 玻璃背景渐变
    const gradient = this.ctx.createLinearGradient(
      x - width/2, y - height/2,
      x + width/2, y + height/2
    )

    if (config.isHovered) {
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.25)')
      gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.15)')
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0.1)')
    } else {
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.2)')
      gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)')
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0.05)')
    }

    this.ctx.fillStyle = gradient
    this.ctx.fill()

    // 玻璃边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    this.ctx.lineWidth = 1
    this.ctx.stroke()

    this.ctx.restore()
  }

  /**
   * 绘制玻璃高光效果
   */
  drawGlassHighlight(x, y, width, height, radius, isHovered) {
    this.ctx.save()

    // 上半部分高光
    const highlightHeight = height * 0.4
    this.drawRoundedRect(
      x - width/2 + 2,
      y - height/2 + 2,
      width - 4,
      highlightHeight,
      radius * 0.8
    )

    // 高光渐变
    const highlightGradient = this.ctx.createLinearGradient(
      x, y - height/2,
      x, y - height/2 + highlightHeight
    )

    const intensity = isHovered ? 0.4 : 0.3
    highlightGradient.addColorStop(0, `rgba(255, 255, 255, ${intensity})`)
    highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

    this.ctx.fillStyle = highlightGradient
    this.ctx.fill()

    this.ctx.restore()
  }

  /**
   * 绘制圆角矩形路径
   */
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath()
    this.ctx.moveTo(x + radius, y)
    this.ctx.lineTo(x + width - radius, y)
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    this.ctx.lineTo(x + width, y + height - radius)
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    this.ctx.lineTo(x + radius, y + height)
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    this.ctx.lineTo(x, y + radius)
    this.ctx.quadraticCurveTo(x, y, x + radius, y)
    this.ctx.closePath()
  }

  /**
   * 绘制像素化边框
   */
  drawPixelBorder(x, y, width, height, color, borderWidth = 2) {
    this.ctx.fillStyle = color
    
    // 上边框
    this.ctx.fillRect(x, y, width, borderWidth)
    // 下边框
    this.ctx.fillRect(x, y + height - borderWidth, width, borderWidth)
    // 左边框
    this.ctx.fillRect(x, y, borderWidth, height)
    // 右边框
    this.ctx.fillRect(x + width - borderWidth, y, borderWidth, height)
    
    // 角落装饰
    this.drawCornerDecorations(x, y, width, height, color, borderWidth)
  }

  /**
   * 绘制角落装饰
   */
  drawCornerDecorations(x, y, width, height, color, size) {
    this.ctx.fillStyle = color
    const decorSize = size * 2
    
    // 左上角
    this.ctx.fillRect(x - decorSize, y - decorSize, decorSize, decorSize)
    // 右上角
    this.ctx.fillRect(x + width, y - decorSize, decorSize, decorSize)
    // 左下角
    this.ctx.fillRect(x - decorSize, y + height, decorSize, decorSize)
    // 右下角
    this.ctx.fillRect(x + width, y + height, decorSize, decorSize)
  }

  /**
   * 绘制光晕效果
   */
  drawGlowEffect(x, y, width, height) {
    const glowSize = 10
    const gradient = this.ctx.createRadialGradient(
      x, y, 0,
      x, y, Math.max(width, height) / 2 + glowSize
    )
    
    gradient.addColorStop(0, Y2KUIConfig.COLORS.ALPHA.GLOW_EFFECT)
    gradient.addColorStop(1, 'rgba(0, 255, 255, 0)')
    
    this.ctx.fillStyle = gradient
    this.ctx.fillRect(
      x - width / 2 - glowSize,
      y - height / 2 - glowSize,
      width + glowSize * 2,
      height + glowSize * 2
    )
  }

  /**
   * 绘制CRT扫描线效果
   */
  drawScanLines() {
    if (!Y2KUIConfig.EFFECTS.CRT_EFFECT.scanLines) return
    
    this.ctx.fillStyle = Y2KUIConfig.COLORS.ALPHA.SCAN_LINE
    
    const lineSpacing = Y2KUIConfig.ANIMATIONS.SCAN_LINES.spacing
    const canvasHeight = this.canvas.height
    
    for (let y = this.scanLineOffset; y < canvasHeight; y += lineSpacing) {
      this.ctx.fillRect(0, y, this.canvas.width, 1)
    }
    
    // 更新扫描线偏移
    this.scanLineOffset += Y2KUIConfig.ANIMATIONS.SCAN_LINES.speed
    if (this.scanLineOffset >= lineSpacing) {
      this.scanLineOffset = 0
    }
  }

  /**
   * 绘制像素噪点
   */
  drawPixelNoise(intensity = 0.1) {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height)
    const data = imageData.data
    
    for (let i = 0; i < data.length; i += 4) {
      if (Math.random() < intensity) {
        const noise = Math.random() * 255
        data[i] = noise     // R
        data[i + 1] = noise // G
        data[i + 2] = noise // B
        // Alpha保持不变
      }
    }
    
    this.ctx.putImageData(imageData, 0, 0)
  }

  /**
   * 绘制像素化星云
   */
  drawPixelNebula(x, y, width, height) {
    const pixelSize = 4
    const cols = Math.floor(width / pixelSize)
    const rows = Math.floor(height / pixelSize)

    for (let i = 0; i < cols; i++) {
      for (let j = 0; j < rows; j++) {
        const pixelX = x + i * pixelSize
        const pixelY = y + j * pixelSize

        // 使用噪声函数创建星云效果
        const noise = this.noise(i * 0.1, j * 0.1, this.animationTime * 0.0001)

        if (noise > 0.3) {
          const intensity = (noise - 0.3) / 0.7
          const alpha = intensity * 0.6

          // 根据强度选择颜色
          let color
          if (intensity > 0.8) {
            color = Y2KUIConfig.COLORS.DIGITAL_WHITE
          } else if (intensity > 0.5) {
            color = Y2KUIConfig.COLORS.CYBER_CYAN
          } else {
            color = Y2KUIConfig.COLORS.ELECTRIC_PURPLE
          }

          this.ctx.fillStyle = color + Math.floor(alpha * 255).toString(16).padStart(2, '0')
          this.ctx.fillRect(pixelX, pixelY, pixelSize, pixelSize)
        }
      }
    }
  }

  /**
   * 绘制数字雨效果（装饰用）
   */
  drawDigitalRain(x, y, width, height) {
    const chars = '01'
    const fontSize = 12
    const columns = Math.floor(width / fontSize)

    this.ctx.font = `${fontSize}px monospace`
    this.ctx.fillStyle = Y2KUIConfig.COLORS.MATRIX_GREEN + '40' // 降低透明度

    // 减少字符密度
    for (let i = 0; i < columns; i += 3) { // 每3列显示一个字符
      if (Math.random() < 0.1) { // 只有10%的概率显示
        const char = chars[Math.floor(Math.random() * chars.length)]
        const charX = x + i * fontSize
        const charY = y + (Math.random() * height)

        this.ctx.fillText(char, charX, charY)
      }
    }
  }

  /**
   * 绘制电路图案
   */
  drawCircuitPattern(x, y, width, height) {
    this.ctx.strokeStyle = Y2KUIConfig.COLORS.CYBER_CYAN
    this.ctx.lineWidth = 1
    
    const gridSize = 20
    const cols = Math.floor(width / gridSize)
    const rows = Math.floor(height / gridSize)
    
    for (let i = 0; i < cols; i++) {
      for (let j = 0; j < rows; j++) {
        if (Math.random() > 0.7) {
          const startX = x + i * gridSize
          const startY = y + j * gridSize
          
          this.ctx.beginPath()
          this.ctx.moveTo(startX, startY)
          
          // 随机绘制水平或垂直线
          if (Math.random() > 0.5) {
            this.ctx.lineTo(startX + gridSize, startY)
          } else {
            this.ctx.lineTo(startX, startY + gridSize)
          }
          
          this.ctx.stroke()
        }
      }
    }
  }

  /**
   * 简单噪声函数
   */
  noise(x, y, z = 0) {
    return (Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453) % 1
  }

  /**
   * 工具函数：使颜色变暗
   */
  darkenColor(color, factor) {
    const hex = color.replace('#', '')
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) * (1 - factor))
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) * (1 - factor))
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) * (1 - factor))

    return `rgb(${Math.floor(r)}, ${Math.floor(g)}, ${Math.floor(b)})`
  }

  /**
   * 更新动画效果
   */
  update(deltaTime) {
    this.glitchTimer += deltaTime
    this.animationTime += deltaTime

    // 更新扫描线
    this.scanLineOffset += Y2KUIConfig.ANIMATIONS.SCAN_LINES.speed
    if (this.scanLineOffset >= Y2KUIConfig.ANIMATIONS.SCAN_LINES.spacing) {
      this.scanLineOffset = 0
    }
  }

  /**
   * 渲染所有像素效果
   */
  render() {
    // 绘制扫描线
    this.drawScanLines()

    // 移除噪点效果，避免花屏
    // if (Math.random() < 0.02) {
    //   this.drawPixelNoise(0.05)
    // }
  }
}
