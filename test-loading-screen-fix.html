<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加载页面修复验证</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            font-family: monospace;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #00FFFF;
        }
        
        .status {
            background: rgba(0, 255, 255, 0.1);
            border: 2px solid #00FFFF;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            text-align: center;
            max-width: 600px;
        }
        
        .success {
            border-color: #00FF41;
            color: #00FF41;
        }
        
        .error {
            border-color: #FF0080;
            color: #FF0080;
        }
        
        h1 {
            color: #00FFFF;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            font-family: monospace;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .fix-details {
            background: rgba(0, 255, 65, 0.1);
            border: 1px solid #00FF41;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            text-align: left;
        }
        
        .fix-details h3 {
            color: #00FF41;
            margin-top: 0;
        }
        
        .fix-details ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .fix-details li {
            margin: 5px 0;
            color: #FFFFFF;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #666;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            color: #FF6600;
            overflow-x: auto;
            font-size: 12px;
        }
        
        .highlight {
            background: rgba(255, 255, 0, 0.2);
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔧 加载页面错误修复</h1>
    
    <div class="status success">
        <h2>✅ pixelEffects undefined 错误已修复</h2>
        <p>LoadingScreen现在可以正常渲染，支持玻璃拟态风格和传统风格的回退机制！</p>
    </div>
    
    <div class="fix-details">
        <h3>🐛 问题分析</h3>
        <ul>
            <li><strong>错误类型：</strong> TypeError: Cannot read property 'drawPixelButton' of undefined</li>
            <li><strong>错误位置：</strong> LoadingScreen.render() 方法</li>
            <li><strong>错误原因：</strong> LoadingScreen在初始化时，pixelEffects还未创建</li>
            <li><strong>初始化顺序：</strong> LoadingScreen → MenuScene → pixelEffects</li>
        </ul>
    </div>
    
    <div class="fix-details">
        <h3>🔧 修复策略</h3>
        
        <h4>1. 延迟获取 pixelEffects</h4>
        <div class="code-block">
// 构造函数中不直接获取
constructor(main) {
    // ...
    this.pixelEffects = <span class="highlight">null</span> // 延迟获取
}

// 在render方法中动态获取
render(ctx) {
    if (!this.isVisible) return
    
    // <span class="highlight">延迟获取pixelEffects（当场景初始化后）</span>
    if (!this.pixelEffects && this.main.menuScene && this.main.menuScene.pixelEffects) {
        this.pixelEffects = this.main.menuScene.pixelEffects
    }
}
        </div>
        
        <h4>2. 双重渲染模式</h4>
        <div class="code-block">
// 智能渲染模式选择
if (this.pixelEffects) {
    // <span class="highlight">玻璃拟态风格</span> - 使用Y2K效果系统
    const boxConfig = {
        color: Y2KUIConfig.COLORS.ALPHA.BACKGROUND_OVERLAY,
        borderColor: Y2KUIConfig.COLORS.CYBER_CYAN,
        glassStyle: true
    }
    this.pixelEffects.drawPixelButton(x, y, width, height, boxConfig)
} else {
    // <span class="highlight">传统风格回退</span> - 使用原生Canvas API
    ctx.fillStyle = '#2C3E50'
    ctx.fillRect(boxX, boxY, boxWidth, boxHeight)
    ctx.strokeStyle = '#34495E'
    ctx.strokeRect(boxX, boxY, boxWidth, boxHeight)
}
        </div>
    </div>
    
    <div class="fix-details">
        <h3>🎨 渲染模式对比</h3>
        
        <h4>🌟 玻璃拟态模式（pixelEffects可用时）</h4>
        <ul>
            <li>Y2K赛博朋克风格加载框</li>
            <li>玻璃拟态进度条</li>
            <li>像素化字体和特效</li>
            <li>统一的视觉风格</li>
        </ul>
        
        <h4>🔄 传统模式（pixelEffects不可用时）</h4>
        <ul>
            <li>经典的加载框样式</li>
            <li>标准进度条</li>
            <li>Arial字体</li>
            <li>确保兼容性</li>
        </ul>
    </div>
    
    <div class="fix-details">
        <h3>📋 修复范围</h3>
        <ul>
            <li><strong>✅ 加载框背景</strong> - 玻璃拟态 / 传统风格</li>
            <li><strong>✅ 标题文字</strong> - Y2K像素风格 / Arial字体</li>
            <li><strong>✅ 消息文字</strong> - 玻璃效果 / 传统阴影</li>
            <li><strong>✅ 进度条</strong> - 玻璃拟态 / 标准样式</li>
            <li><strong>✅ 百分比显示</strong> - 数字风格 / 传统字体</li>
            <li><strong>✅ 错误提示</strong> - Y2K风格 / 传统样式</li>
            <li><strong>✅ 重试按钮</strong> - 玻璃拟态 / 标准按钮</li>
        </ul>
    </div>
    
    <div class="fix-details">
        <h3>🔄 初始化流程优化</h3>
        
        <div class="code-block">
// 修复前的问题流程
Main.constructor() {
    this.loadingScreen = new LoadingScreen(this) // ❌ pixelEffects还不存在
    this.initScenes() // 这里才创建MenuScene和pixelEffects
}

// 修复后的安全流程
LoadingScreen.render() {
    // ✅ 动态检查和获取pixelEffects
    if (!this.pixelEffects && this.main.menuScene?.pixelEffects) {
        this.pixelEffects = this.main.menuScene.pixelEffects
    }
    
    // ✅ 根据可用性选择渲染模式
    if (this.pixelEffects) {
        // 使用高级效果
    } else {
        // 使用基础渲染
    }
}
        </div>
    </div>
    
    <div class="status success">
        <h2>🎯 修复验证</h2>
        <p><strong>现在LoadingScreen具备以下特性：</strong></p>
        <ul style="text-align: left; display: inline-block;">
            <li>✅ 不再出现 pixelEffects undefined 错误</li>
            <li>✅ 支持玻璃拟态风格（当可用时）</li>
            <li>✅ 自动回退到传统风格（当不可用时）</li>
            <li>✅ 保持视觉一致性</li>
            <li>✅ 确保加载过程稳定</li>
            <li>✅ 兼容不同初始化阶段</li>
        </ul>
    </div>
    
    <div class="fix-details">
        <h3>🚀 技术亮点</h3>
        <ul>
            <li><strong>智能检测：</strong> 动态检测pixelEffects可用性</li>
            <li><strong>优雅降级：</strong> 无缝回退到传统渲染</li>
            <li><strong>零错误：</strong> 消除undefined属性访问</li>
            <li><strong>视觉统一：</strong> 尽可能保持Y2K风格</li>
            <li><strong>性能优化：</strong> 避免重复检查和创建</li>
        </ul>
    </div>
</body>
</html>
