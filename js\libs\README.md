# 物理引擎说明

## 当前状态

项目现在支持**Box2D WASM** 和**简化物理引擎**两种模式，会自动选择可用的物理引擎。

## 物理引擎特性

### Box2D WASM（优先使用）
- ✅ 重力模拟
- ✅ 碰撞检测
- ✅ 弹性碰撞
- ✅ 摩擦力
- ✅ 水果合成检测
- ✅ 连锁反应
- ✅ 高性能物理计算

### 简化物理引擎（备用方案）
- ✅ 重力模拟
- ✅ 碰撞检测
- ✅ 弹性碰撞
- ✅ 摩擦力
- ✅ 水果合成检测
- ✅ 连锁反应
- ✅ 兼容性好

## 文件放置位置

### 推荐的文件结构

```
js/libs/
├── box2d-wasm.js      # JavaScript 包装器（已创建）
├── box2d-wasm.wasm    # WebAssembly 模块（需要放置）
├── weapp-adapter.js   # 微信小游戏适配器
├── symbol.js          # Symbol polyfill
└── README.md          # 本文件
```

### 下载方法

#### 方法一：从官方仓库下载

1. 访问 [Box2D WASM 官方仓库](https://github.com/kripken/box2d.js)
2. 下载 `box2d-wasm.wasm` 文件
3. 将文件放入 `js/libs/` 目录

#### 方法二：使用 CDN

```bash
# 下载 Box2D WASM 文件
curl -o js/libs/box2d-wasm.wasm https://cdn.jsdelivr.net/npm/box2d-wasm@latest/box2d-wasm.wasm
```

#### 方法三：从 npm 安装

```bash
# 安装 box2d-wasm 包
npm install box2d-wasm

# 复制文件到项目
cp node_modules/box2d-wasm/box2d-wasm.wasm js/libs/
```

## 注意事项

1. **自动选择**：项目会自动检测并选择可用的物理引擎。

2. **性能对比**：Box2D WASM 提供更高性能的物理计算。

3. **兼容性**：简化物理引擎确保在所有环境下都能正常工作。

4. **功能完整**：两种引擎都包含所有必要的物理特性。

## 测试

重新编译项目，游戏会自动选择物理引擎：
- 如果有 `box2d-wasm.wasm` 文件，会使用 Box2D WASM
- 如果没有或加载失败，会使用简化物理引擎
- 控制台会显示使用的物理引擎类型

## 故障排除

如果遇到问题：

1. **物理引擎不工作**：检查控制台是否有错误信息
2. **性能问题**：可以调整物理引擎的迭代次数
3. **碰撞检测异常**：检查水果对象的半径设置
4. **合成不触发**：确保水果等级相同且速度足够小 