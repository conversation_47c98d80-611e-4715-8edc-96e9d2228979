<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            font-family: monospace;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #00FFFF;
        }
        
        canvas {
            border: 2px solid #00FFFF;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            background: #0a0a0a;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .control-button {
            padding: 12px 24px;
            background: rgba(0, 255, 255, 0.1);
            border: 2px solid #00FFFF;
            color: #00FFFF;
            cursor: pointer;
            font-family: monospace;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .control-button:hover {
            background: rgba(0, 255, 255, 0.2);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        h1 {
            color: #00FFFF;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            font-family: monospace;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .info {
            margin-top: 15px;
            text-align: center;
            color: #00FF41;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🎮 弹窗修复测试</h1>
    <canvas id="testCanvas" width="800" height="600"></canvas>
    
    <div class="controls">
        <button class="control-button" onclick="showAchievements()">成就系统</button>
        <button class="control-button" onclick="showRules()">游戏规则</button>
        <button class="control-button" onclick="closeModal()">关闭弹窗</button>
    </div>
    
    <div class="info">
        测试弹窗文字显示和布局优化
    </div>

    <script>
        // 简化的配置
        const Y2KUIConfig = {
            COLORS: {
                CYBER_CYAN: '#00FFFF',
                MATRIX_GREEN: '#00FF41',
                PIXEL_BLACK: '#0A0A0A',
                DIGITAL_WHITE: '#FFFFFF',
                NEON_PINK: '#FF0080',
                ELECTRIC_PURPLE: '#8A2BE2',
                LIQUID_ORANGE: '#FF6600',
                METAL_SILVER: '#C0C0C0',
                ALPHA: {
                    BACKGROUND_OVERLAY: 'rgba(10, 10, 10, 0.85)',
                    GLOW_EFFECT: 'rgba(0, 255, 255, 0.3)',
                    SCAN_LINE: 'rgba(255, 255, 255, 0.1)'
                }
            },
            FONTS: {
                PIXEL_TITLE: { size: 24, weight: 'bold', family: 'monospace' },
                PIXEL_BUTTON: { size: 16, weight: 'bold', family: 'monospace' },
                SYSTEM_HINT: { size: 12, weight: 'normal', family: 'monospace' }
            },
            DIMENSIONS: {
                MODAL_PANEL: { width: 420, height: 550 },
                PIXEL_BORDER: 3
            }
        };

        class ModalTest {
            constructor() {
                this.canvas = document.getElementById('testCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.showModal = false;
                this.modalType = 'achievements';
                this.modalTitle = '';
                this.modalScrollY = 0;
                
                this.achievements = [
                    { id: 'first_merge', name: '初出茅庐', desc: '完成第一次合成', unlocked: true },
                    { id: 'score_100', name: '小有成就', desc: '累计得分达到100分', unlocked: true },
                    { id: 'score_1000', name: '事业有成', desc: '累计得分达到1000分', unlocked: false },
                    { id: 'score_10000', name: '财富自由', desc: '累计得分达到10000分', unlocked: false },
                    { id: 'rich_old_man', name: '人生巅峰', desc: '成功合成富老炮', unlocked: false },
                    { id: 'merge_100', name: '合成大师', desc: '完成100次合成', unlocked: false },
                    { id: 'time_5min', name: '时间管理', desc: '单局游戏时间超过5分钟', unlocked: false },
                    { id: 'speed_1000', name: '速战速决', desc: '3分钟内达到1000分', unlocked: false }
                ];
                
                this.init();
            }
            
            init() {
                this.render();
                this.animate();
            }
            
            render() {
                // 清空画布
                this.ctx.fillStyle = '#0a0a0a';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 绘制背景网格
                this.drawGrid();
                
                // 绘制主界面
                this.ctx.font = '32px monospace';
                this.ctx.fillStyle = Y2KUIConfig.COLORS.CYBER_CYAN;
                this.ctx.textAlign = 'center';
                this.ctx.fillText('弹窗测试界面', this.canvas.width / 2, this.canvas.height / 2);
                
                this.ctx.font = '16px monospace';
                this.ctx.fillStyle = Y2KUIConfig.COLORS.MATRIX_GREEN;
                this.ctx.fillText('点击下方按钮测试弹窗效果', this.canvas.width / 2, this.canvas.height / 2 + 40);
                
                // 如果显示弹窗，绘制弹窗
                if (this.showModal) {
                    this.renderModal();
                }
            }
            
            drawGrid() {
                this.ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
                this.ctx.lineWidth = 1;
                
                for (let x = 0; x < this.canvas.width; x += 20) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, this.canvas.height);
                    this.ctx.stroke();
                }
                
                for (let y = 0; y < this.canvas.height; y += 20) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(this.canvas.width, y);
                    this.ctx.stroke();
                }
            }
            
            renderModal() {
                // 绘制半透明背景
                this.ctx.fillStyle = Y2KUIConfig.COLORS.ALPHA.BACKGROUND_OVERLAY;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // 弹窗尺寸和位置
                const modalWidth = Y2KUIConfig.DIMENSIONS.MODAL_PANEL.width;
                const modalHeight = Y2KUIConfig.DIMENSIONS.MODAL_PANEL.height;
                const modalX = (this.canvas.width - modalWidth) / 2;
                const modalY = (this.canvas.height - modalHeight) / 2;

                // 绘制弹窗背景
                this.ctx.fillStyle = Y2KUIConfig.COLORS.PIXEL_BLACK;
                this.ctx.fillRect(modalX, modalY, modalWidth, modalHeight);

                // 绘制边框
                this.ctx.strokeStyle = Y2KUIConfig.COLORS.CYBER_CYAN;
                this.ctx.lineWidth = 3;
                this.ctx.strokeRect(modalX, modalY, modalWidth, modalHeight);

                // 绘制标题
                this.ctx.font = '24px monospace';
                this.ctx.fillStyle = Y2KUIConfig.COLORS.CYBER_CYAN;
                this.ctx.textAlign = 'center';
                this.ctx.fillText(this.modalTitle, this.canvas.width / 2, modalY + 40);

                // 绘制内容
                const contentX = modalX + 20;
                const contentY = modalY + 80;
                const contentWidth = modalWidth - 40;
                const contentHeight = modalHeight - 160;

                if (this.modalType === 'achievements') {
                    this.renderAchievements(contentX, contentY, contentWidth, contentHeight);
                } else {
                    this.renderRules(contentX, contentY, contentWidth, contentHeight);
                }

                // 绘制关闭按钮
                const buttonY = modalY + modalHeight - 60;
                this.ctx.fillStyle = Y2KUIConfig.COLORS.NEON_PINK;
                this.ctx.fillRect(this.canvas.width / 2 - 100, buttonY - 25, 200, 50);
                
                this.ctx.strokeStyle = Y2KUIConfig.COLORS.DIGITAL_WHITE;
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(this.canvas.width / 2 - 100, buttonY - 25, 200, 50);
                
                this.ctx.font = '16px monospace';
                this.ctx.fillStyle = Y2KUIConfig.COLORS.DIGITAL_WHITE;
                this.ctx.fillText('继续游戏', this.canvas.width / 2, buttonY);
            }
            
            renderAchievements(contentX, contentY, contentWidth, contentHeight) {
                let currentY = contentY + 20;
                const cardHeight = 80;
                const cardSpacing = 15;
                
                // 绘制标题
                this.ctx.font = '18px monospace';
                this.ctx.fillStyle = Y2KUIConfig.COLORS.LIQUID_ORANGE;
                this.ctx.textAlign = 'center';
                this.ctx.fillText('🎯 游戏成就', contentX + contentWidth / 2, currentY);
                currentY += 40;
                
                // 绘制成就卡片
                for (let achievement of this.achievements) {
                    if (currentY + cardHeight > contentY + contentHeight) break;
                    
                    // 卡片背景
                    if (achievement.unlocked) {
                        this.ctx.fillStyle = 'rgba(0, 255, 65, 0.15)';
                        this.ctx.strokeStyle = Y2KUIConfig.COLORS.MATRIX_GREEN;
                    } else {
                        this.ctx.fillStyle = 'rgba(149, 165, 166, 0.15)';
                        this.ctx.strokeStyle = Y2KUIConfig.COLORS.METAL_SILVER;
                    }
                    
                    this.ctx.fillRect(contentX, currentY, contentWidth - 20, cardHeight);
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(contentX, currentY, contentWidth - 20, cardHeight);
                    
                    // 图标
                    this.ctx.font = '20px monospace';
                    this.ctx.fillStyle = achievement.unlocked ? Y2KUIConfig.COLORS.MATRIX_GREEN : Y2KUIConfig.COLORS.LIQUID_ORANGE;
                    this.ctx.textAlign = 'left';
                    this.ctx.fillText(achievement.unlocked ? '🏆' : '🔒', contentX + 15, currentY + 30);
                    
                    // 名称
                    this.ctx.font = '14px monospace';
                    this.ctx.fillStyle = achievement.unlocked ? Y2KUIConfig.COLORS.MATRIX_GREEN : Y2KUIConfig.COLORS.DIGITAL_WHITE;
                    this.ctx.fillText(achievement.name, contentX + 50, currentY + 25);
                    
                    // 描述
                    this.ctx.font = '11px monospace';
                    this.ctx.fillStyle = achievement.unlocked ? Y2KUIConfig.COLORS.CYBER_CYAN : Y2KUIConfig.COLORS.METAL_SILVER;
                    this.ctx.fillText(achievement.desc, contentX + 50, currentY + 45);
                    
                    // 状态
                    this.ctx.font = '10px monospace';
                    this.ctx.textAlign = 'right';
                    this.ctx.fillText(achievement.unlocked ? '已解锁' : '未解锁', contentX + contentWidth - 30, currentY + 35);
                    
                    currentY += cardHeight + cardSpacing;
                }
            }
            
            renderRules(contentX, contentY, contentWidth, contentHeight) {
                const rules = [
                    '# 游戏规则',
                    '',
                    '## 基本玩法',
                    '- 点击屏幕任意位置掉落角色',
                    '- 相同等级的角色碰撞会合成更高级角色',
                    '- 不同等级的角色会形成堆叠',
                    '',
                    '## 游戏目标',
                    '- 合成终极角色富老炮',
                    '- 获得更高的分数',
                    '- 解锁所有成就',
                    '',
                    '## 特殊功能',
                    '- 点击丐帮主可获得666分奖励',
                    '- 背景会随着最高等级角色变化'
                ];
                
                let currentY = contentY + 20;
                const lineHeight = 22;
                
                for (let line of rules) {
                    if (currentY > contentY + contentHeight) break;
                    
                    let fontSize = 12;
                    let color = Y2KUIConfig.COLORS.DIGITAL_WHITE;
                    
                    if (line.startsWith('##')) {
                        fontSize = 16;
                        color = Y2KUIConfig.COLORS.CYBER_CYAN;
                        line = line.replace('##', '').trim();
                    } else if (line.startsWith('#')) {
                        fontSize = 18;
                        color = Y2KUIConfig.COLORS.LIQUID_ORANGE;
                        line = line.replace('#', '').trim();
                    } else if (line.startsWith('-')) {
                        fontSize = 12;
                        color = Y2KUIConfig.COLORS.MATRIX_GREEN;
                    }
                    
                    if (line.trim()) {
                        this.ctx.font = `${fontSize}px monospace`;
                        this.ctx.fillStyle = color;
                        this.ctx.textAlign = 'left';
                        this.ctx.fillText(line, contentX + 10, currentY);
                    }
                    
                    currentY += lineHeight;
                }
            }
            
            animate() {
                this.render();
                requestAnimationFrame(() => this.animate());
            }
        }
        
        let test;
        
        window.onload = () => {
            test = new ModalTest();
        };
        
        function showAchievements() {
            test.modalType = 'achievements';
            test.modalTitle = '🏆 成就系统';
            test.showModal = true;
        }
        
        function showRules() {
            test.modalType = 'rules';
            test.modalTitle = '📖 游戏规则';
            test.showModal = true;
        }
        
        function closeModal() {
            test.showModal = false;
        }
    </script>
</body>
</html>
