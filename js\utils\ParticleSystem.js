export class ParticleSystem {
  constructor() {
    this.particles = []
  }
  
  // 创建单个粒子
  createParticle(x, y, vx, vy, color, size, life) {
    // 限制粒子数量，防止内存泄漏
    if (this.particles.length > 200) {
      this.particles = this.particles.slice(-100) // 保留最新的100个粒子
    }
    
    this.particles.push({
      x: x,
      y: y,
      vx: vx,
      vy: vy,
      life: life,
      maxLife: life,
      color: color,
      size: size,
      gravity: 0.05,
      fade: true
    })
  }
  
  // 创建爆炸粒子效果
  createExplosion(x, y, color = '#FFD700', count = 15) {
    for (let i = 0; i < count; i++) {
      const angle = (Math.PI * 2 * i) / count
      const speed = 2 + Math.random() * 3
      
      this.particles.push({
        x: x,
        y: y,
        vx: Math.cos(angle) * speed,
        vy: Math.sin(angle) * speed,
        life: 60 + Math.random() * 30,
        maxLife: 90,
        color: color,
        size: 2 + Math.random() * 3,
        gravity: 0.1,
        fade: true
      })
    }
  }
  
  // 创建合并特效
  createMergeEffect(x, y, score) {
    // 创建得分文字粒子
    this.particles.push({
      x: x,
      y: y,
      vx: 0,
      vy: -2,
      life: 90,
      maxLife: 90,
      text: `+${score}`,
      color: '#FFD700',
      size: 20,
      gravity: 0.05,
      fade: true,
      isText: true
    })
    
    // 创建光环效果
    for (let i = 0; i < 8; i++) {
      const angle = (Math.PI * 2 * i) / 8
      const speed = 1 + Math.random() * 2
      
      this.particles.push({
        x: x,
        y: y,
        vx: Math.cos(angle) * speed,
        vy: Math.sin(angle) * speed,
        life: 45,
        maxLife: 45,
        color: '#FFFFFF',
        size: 1 + Math.random() * 2,
        gravity: 0.05,
        fade: true
      })
    }
  }
  
  // 创建拖尾效果
  createTrail(x, y, color = '#FFFFFF') {
    this.particles.push({
      x: x,
      y: y,
      vx: (Math.random() - 0.5) * 2,
      vy: (Math.random() - 0.5) * 2,
      life: 30,
      maxLife: 30,
      color: color,
      size: 1 + Math.random() * 2,
      gravity: 0.05,
      fade: true
    })
  }
  
  // 创建星星特效
  createStars(x, y, count = 5) {
    for (let i = 0; i < count; i++) {
      this.particles.push({
        x: x + (Math.random() - 0.5) * 100,
        y: y + (Math.random() - 0.5) * 100,
        vx: (Math.random() - 0.5) * 3,
        vy: (Math.random() - 0.5) * 3,
        life: 120,
        maxLife: 120,
        color: '#FFFF00',
        size: 1 + Math.random() * 2,
        gravity: 0.02,
        fade: true,
        twinkle: true
      })
    }
  }
  
  // 更新所有粒子
  update() {
    this.particles = this.particles.filter(particle => {
      // 更新位置
      particle.x += particle.vx
      particle.y += particle.vy
      
      // 应用重力
      if (particle.gravity) {
        particle.vy += particle.gravity
      }
      
      // 减少生命值
      particle.life--
      
      // 闪烁效果
      if (particle.twinkle) {
        particle.alpha = 0.5 + 0.5 * Math.sin(particle.life * 0.2)
      }
      
      return particle.life > 0
    })
  }
  
  // 渲染所有粒子
  render(ctx) {
    this.particles.forEach(particle => {
      ctx.save()
      
      // 计算透明度
      let alpha = 1
      if (particle.fade) {
        alpha = particle.life / particle.maxLife
      }
      if (particle.alpha !== undefined) {
        alpha *= particle.alpha
      }
      
      ctx.globalAlpha = alpha
      
      if (particle.isText) {
        // 渲染文字粒子
        ctx.fillStyle = particle.color
        ctx.font = `bold ${particle.size}px Arial`
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(particle.text, particle.x, particle.y)
      } else {
        // 渲染圆形粒子
        ctx.fillStyle = particle.color
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fill()
      }
      
      ctx.restore()
    })
  }
  
  // 清空所有粒子
  clear() {
    this.particles = []
  }
  
  // 获取粒子数量
  getParticleCount() {
    return this.particles.length
  }
} 