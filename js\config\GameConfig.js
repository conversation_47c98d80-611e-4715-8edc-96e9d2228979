// 游戏配置文件
export const GameConfig = {
  // 游戏基础设置
  GAME: {
    FPS: 60,
    GRAVITY: 0.6, // 增加重力，让球掉落更快
    FRICTION: 0.96, // 减少摩擦力，增加不稳定性
    BOUNCE: 0.8, // 增加弹性，增加混乱度
    TOP_THRESHOLD: 80, // 降低警戒线，增加难度
    LEVEL_UP_SCORE: 1500, // 提高升级所需分数
    WARNING_CONTACT_DURATION: 1000, // 警戒线接触时间缩短到1秒
    MAX_GAME_TIME: 900000 // 15分钟游戏时长限制（毫秒）
  },
  
  // 水果配置 - 关键难度调整
  FRUITS: {
    MIN_LEVEL: 1,
    MAX_LEVEL: 12,
    BASE_RADIUS: 15,
    RADIUS_INCREMENT: 6, // 增加半径增长，让高级球更大更难放置
    BASE_SCORE: 8, // 降低基础分数
    SCORE_MULTIPLIER: 1.8, // 降低分数倍数
    // 新增：生成概率配置
    GENERATION_WEIGHTS: {
      1: 35, // 小宝 - 35%
      2: 30, // 年轻哥 - 30% 
      3: 25, // 走读生 - 25%
      4: 10  // 大满贯 - 10% (偶尔给高级球增加策略性)
    }
  },
  
  // 物理设置 - 增加不可预测性
  PHYSICS: {
    MIN_VELOCITY: 1.5,
    MAX_VELOCITY: 18,
    DRAG_SPEED_MULTIPLIER: 0.08, // 减少拖拽控制精度
    COLLISION_SEPARATION: 0.3, // 减少碰撞分离，增加粘连
    INSTABILITY_FACTOR: 1.2 // 新增：物理不稳定因子
  },
  
  // 粒子特效设置
  PARTICLES: {
    MERGE_COUNT: 8,
    EXPLOSION_COUNT: 15,
    TRAIL_COUNT: 3,
    STAR_COUNT: 5,
    DEFAULT_LIFE: 60,
    FADE_DURATION: 30
  },
  
  // 音频设置
  AUDIO: {
    DEFAULT_VOLUME: 0.7,
    BGM_VOLUME_RATIO: 0.5,
    SOUND_FILES: {
      DROP: 'game_media/audio/drop.mp3',
      CLICK: 'game_media/audio/click.mp3',
      BGM: 'game_media/audio/bgm.mp3'
    }
  },
  
  // UI设置
  UI: {
    BUTTON_WIDTH: 200,
    BUTTON_HEIGHT: 60,
    PANEL_WIDTH: 300,
    PANEL_HEIGHT: 400,
    LAUNCH_AREA_WIDTH: 200,
    LAUNCH_AREA_HEIGHT: 60
  },
  
  // 颜色配置
  COLORS: {
    BACKGROUND_GRADIENT: {
      START: '#87CEEB',
      END: '#4682B4'
    },
    MENU_GRADIENT: {
      START: '#74B9FF',
      END: '#0984E3'
    },
    DANGER_ZONE: 'rgba(255, 0, 0, 0.3)',
    BUTTON_BORDER: 'rgba(255, 255, 255, 0.3)',
    LAUNCH_AREA: 'rgba(255, 255, 255, 0.2)',
    GAME_AREA_BORDER: 'rgba(255, 255, 255, 0.5)',
    TEXT_WHITE: 'white',
    TEXT_GOLD: '#FFD700',
    TEXT_RED: '#E74C3C',
    TEXT_BLUE: '#3498DB',
    TEXT_GREEN: '#2ECC71'
  },
  
  // 字体设置
  FONTS: {
    TITLE: 'bold 48px Arial',
    SUBTITLE: '24px Arial',
    BUTTON: 'bold 24px Arial',
    SCORE: 'bold 24px Arial',
    LEVEL: '24px Arial',
    SMALL: '16px Arial',
    PARTICLE_TEXT: 'bold 20px Arial'
  },
  
  // 动画设置
  ANIMATION: {
    BUTTON_HOVER_DURATION: 200,
    PARTICLE_FADE_DURATION: 30,
    TEXT_FLOAT_DURATION: 60,
    LEVEL_UP_DURATION: 2000
  },
  
  // 存储键名
  STORAGE_KEYS: {
    HIGH_SCORE: 'highScore',
    SETTINGS: 'gameSettings',
    STATISTICS: 'gameStats'
  },
  
  // 调试设置
  DEBUG: {
    SHOW_FPS: false,
    SHOW_COLLISION_BOXES: false,
    SHOW_PARTICLE_COUNT: false,
    LOG_LEVEL: 'warn' // 'debug', 'info', 'warn', 'error'
  },
  
  // 难度递增配置
  DIFFICULTY: {
    // 基于最高等级的难度调整
    LEVEL_MODIFIERS: {
      1: { gravityMultiplier: 1.0, frictionMultiplier: 1.0 },
      2: { gravityMultiplier: 1.0, frictionMultiplier: 1.0 },
      3: { gravityMultiplier: 1.0, frictionMultiplier: 1.0 },
      4: { gravityMultiplier: 1.1, frictionMultiplier: 0.98 },
      5: { gravityMultiplier: 1.1, frictionMultiplier: 0.98 },
      6: { gravityMultiplier: 1.2, frictionMultiplier: 0.96 },
      7: { gravityMultiplier: 1.2, frictionMultiplier: 0.96 },
      8: { gravityMultiplier: 1.3, frictionMultiplier: 0.94 },
      9: { gravityMultiplier: 1.4, frictionMultiplier: 0.92 },
      10: { gravityMultiplier: 1.5, frictionMultiplier: 0.90 }, // 富二代阶段大幅增难
      11: { gravityMultiplier: 1.6, frictionMultiplier: 0.88 },
      12: { gravityMultiplier: 1.7, frictionMultiplier: 0.86 }
    },
    
    // 时间压力配置
    TIME_PRESSURE: {
      ENABLED: true,
      START_TIME: 600000, // 10分钟后开始时间压力
      GRAVITY_INCREASE_RATE: 0.001, // 每秒增加的重力
      MAX_GRAVITY_MULTIPLIER: 2.0
    }
  }
}

// 导出默认配置
export default GameConfig 
