// 游戏配置文件
export const GameConfig = {
  // 游戏基础设置
  GAME: {
    FPS: 60,
    GRAVITY: 0.6, // 增加重力，让球掉落更快
    FRICTION: 0.96, // 减少摩擦力，增加不稳定性
    BOUNCE: 0.8, // 增加弹性，增加混乱度
    TOP_THRESHOLD: 80, // 降低警戒线，增加难度
    LEVEL_UP_SCORE: 1500, // 提高升级所需分数
    WARNING_CONTACT_DURATION: 1000, // 警戒线接触时间缩短到1秒
    MAX_GAME_TIME: 900000 // 15分钟游戏时长限制（毫秒）
  },
  
  // 水果配置 - 羊了个羊式难度调整
  FRUITS: {
    MIN_LEVEL: 1,
    MAX_LEVEL: 12,
    BASE_RADIUS: 15,
    RADIUS_INCREMENT: 6, // 增加半径增长，让高级球更大更难放置
    BASE_SCORE: 5, // 进一步降低基础分数，延长游戏时间
    SCORE_MULTIPLIER: 1.5, // 降低分数倍数，让升级更困难

    // 动态生成概率配置 - 根据游戏阶段调整
    GENERATION_WEIGHTS: {
      // 教学阶段权重 (前100分)
      TUTORIAL: {
        1: 50, // 小宝 - 50%
        2: 35, // 年轻哥 - 35%
        3: 15, // 走读生 - 15%
        4: 0   // 大满贯 - 0%
      },

      // 正常阶段权重 (100-500分)
      NORMAL: {
        1: 40, // 小宝 - 40%
        2: 35, // 年轻哥 - 35%
        3: 20, // 走读生 - 20%
        4: 5   // 大满贯 - 5%
      },

      // 挑战阶段权重 (500-2000分)
      CHALLENGING: {
        1: 30, // 小宝 - 30%
        2: 30, // 年轻哥 - 30%
        3: 25, // 走读生 - 25%
        4: 15  // 大满贯 - 15%
      },

      // 噩梦阶段权重 (2000分+) - 富二代关卡
      NIGHTMARE: {
        1: 20, // 小宝 - 20% (减少低级球)
        2: 25, // 年轻哥 - 25%
        3: 30, // 走读生 - 30%
        4: 25  // 大满贯 - 25% (大幅增加高级球)
      }
    },

    // 恶意生成机制 - 羊了个羊特色
    MALICIOUS_GENERATION: {
      ENABLED: true,

      // 不利球体连续生成
      BAD_STREAK: {
        PROBABILITY: 0.15, // 15%概率触发
        MIN_LENGTH: 3, // 最少连续3个
        MAX_LENGTH: 6, // 最多连续6个
        BAD_BALL_LEVEL: 4 // 生成4级球(大满贯)
      },

      // 空间压迫生成
      SPACE_PRESSURE: {
        ENABLED: true,
        TRIGGER_THRESHOLD: 0.7, // 当屏幕70%被占用时触发
        LARGE_BALL_BIAS: 2.0 // 偏向生成大球的倍数
      },

      // 反策略生成
      ANTI_STRATEGY: {
        ENABLED: true,
        DETECT_PATTERN: true, // 检测玩家策略模式
        COUNTER_PROBABILITY: 0.3 // 30%概率生成反制球体
      }
    }
  },
  
  // 物理设置 - 增加不可预测性
  PHYSICS: {
    MIN_VELOCITY: 1.5,
    MAX_VELOCITY: 18,
    DRAG_SPEED_MULTIPLIER: 0.08, // 减少拖拽控制精度
    COLLISION_SEPARATION: 0.3, // 减少碰撞分离，增加粘连
    INSTABILITY_FACTOR: 1.2 // 新增：物理不稳定因子
  },
  
  // 粒子特效设置
  PARTICLES: {
    MERGE_COUNT: 8,
    EXPLOSION_COUNT: 15,
    TRAIL_COUNT: 3,
    STAR_COUNT: 5,
    DEFAULT_LIFE: 60,
    FADE_DURATION: 30
  },
  
  // 音频设置
  AUDIO: {
    DEFAULT_VOLUME: 0.7,
    BGM_VOLUME_RATIO: 0.5,
    SOUND_FILES: {
      DROP: 'game_media/audio/drop.mp3',
      CLICK: 'game_media/audio/click.mp3',
      BGM: 'game_media/audio/bgm.mp3'
    }
  },
  
  // UI设置
  UI: {
    BUTTON_WIDTH: 200,
    BUTTON_HEIGHT: 60,
    PANEL_WIDTH: 300,
    PANEL_HEIGHT: 400,
    LAUNCH_AREA_WIDTH: 200,
    LAUNCH_AREA_HEIGHT: 60
  },
  
  // 颜色配置
  COLORS: {
    BACKGROUND_GRADIENT: {
      START: '#87CEEB',
      END: '#4682B4'
    },
    MENU_GRADIENT: {
      START: '#74B9FF',
      END: '#0984E3'
    },
    DANGER_ZONE: 'rgba(255, 0, 0, 0.3)',
    BUTTON_BORDER: 'rgba(255, 255, 255, 0.3)',
    LAUNCH_AREA: 'rgba(255, 255, 255, 0.2)',
    GAME_AREA_BORDER: 'rgba(255, 255, 255, 0.5)',
    TEXT_WHITE: 'white',
    TEXT_GOLD: '#FFD700',
    TEXT_RED: '#E74C3C',
    TEXT_BLUE: '#3498DB',
    TEXT_GREEN: '#2ECC71'
  },
  
  // 字体设置
  FONTS: {
    TITLE: 'bold 48px Arial',
    SUBTITLE: '24px Arial',
    BUTTON: 'bold 24px Arial',
    SCORE: 'bold 24px Arial',
    LEVEL: '24px Arial',
    SMALL: '16px Arial',
    PARTICLE_TEXT: 'bold 20px Arial'
  },
  
  // 动画设置
  ANIMATION: {
    BUTTON_HOVER_DURATION: 200,
    PARTICLE_FADE_DURATION: 30,
    TEXT_FLOAT_DURATION: 60,
    LEVEL_UP_DURATION: 2000
  },
  
  // 存储键名
  STORAGE_KEYS: {
    HIGH_SCORE: 'highScore',
    SETTINGS: 'gameSettings',
    STATISTICS: 'gameStats'
  },
  
  // 调试设置
  DEBUG: {
    SHOW_FPS: false,
    SHOW_COLLISION_BOXES: false,
    SHOW_PARTICLE_COUNT: false,
    LOG_LEVEL: 'warn' // 'debug', 'info', 'warn', 'error'
  },
  
  // 难度递增配置 - 参考"羊了个羊"设计
  DIFFICULTY: {
    // 基于最高等级的难度调整 - 让富二代阶段(10级)成为主要失败点
    LEVEL_MODIFIERS: {
      1: { gravityMultiplier: 1.0, frictionMultiplier: 1.0, instabilityFactor: 1.0 },
      2: { gravityMultiplier: 1.0, frictionMultiplier: 1.0, instabilityFactor: 1.0 },
      3: { gravityMultiplier: 1.0, frictionMultiplier: 1.0, instabilityFactor: 1.0 },
      4: { gravityMultiplier: 1.05, frictionMultiplier: 0.99, instabilityFactor: 1.1 },
      5: { gravityMultiplier: 1.1, frictionMultiplier: 0.98, instabilityFactor: 1.1 },
      6: { gravityMultiplier: 1.15, frictionMultiplier: 0.97, instabilityFactor: 1.2 },
      7: { gravityMultiplier: 1.2, frictionMultiplier: 0.96, instabilityFactor: 1.2 },
      8: { gravityMultiplier: 1.3, frictionMultiplier: 0.94, instabilityFactor: 1.3 },
      9: { gravityMultiplier: 1.4, frictionMultiplier: 0.92, instabilityFactor: 1.4 },
      10: { gravityMultiplier: 1.8, frictionMultiplier: 0.85, instabilityFactor: 1.8 }, // 富二代阶段：极高难度
      11: { gravityMultiplier: 2.0, frictionMultiplier: 0.80, instabilityFactor: 2.0 },
      12: { gravityMultiplier: 2.2, frictionMultiplier: 0.75, instabilityFactor: 2.2 }
    },

    // 时间压力配置 - 15分钟游戏时长控制
    TIME_PRESSURE: {
      ENABLED: true,
      START_TIME: 600000, // 10分钟后开始时间压力
      GRAVITY_INCREASE_RATE: 0.002, // 每秒增加的重力（加快）
      MAX_GRAVITY_MULTIPLIER: 3.0, // 提高最大重力倍数
      FRICTION_DECREASE_RATE: 0.001, // 每秒减少的摩擦力
      MIN_FRICTION_MULTIPLIER: 0.7 // 最小摩擦力倍数
    },

    // 羊了个羊式难度曲线
    SHEEP_DIFFICULTY: {
      // 阶段性难度设计
      PHASES: {
        TUTORIAL: { // 教学阶段 (1-3级)
          scoreRange: [0, 100],
          difficultyMultiplier: 0.8,
          generationBias: 0.1 // 偏向生成低级球
        },
        NORMAL: { // 正常阶段 (4-6级)
          scoreRange: [100, 500],
          difficultyMultiplier: 1.0,
          generationBias: 0.0
        },
        CHALLENGING: { // 挑战阶段 (7-9级)
          scoreRange: [500, 2000],
          difficultyMultiplier: 1.3,
          generationBias: -0.1 // 偏向生成高级球
        },
        NIGHTMARE: { // 噩梦阶段 (10级+) - 富二代关卡
          scoreRange: [2000, Infinity],
          difficultyMultiplier: 2.0,
          generationBias: -0.2, // 强烈偏向生成高级球
          chaosMode: true // 开启混乱模式
        }
      },

      // 混乱模式配置
      CHAOS_MODE: {
        RANDOM_GRAVITY_SPIKES: true, // 随机重力突增
        SPIKE_PROBABILITY: 0.05, // 每帧5%概率触发
        SPIKE_MULTIPLIER: 2.5, // 重力突增倍数
        SPIKE_DURATION: 1000, // 持续时间(ms)

        FRICTION_CHAOS: true, // 摩擦力混乱
        FRICTION_VARIANCE: 0.3, // 摩擦力变化幅度

        GENERATION_CHAOS: true, // 生成混乱
        BAD_LUCK_STREAK: 0.15 // 15%概率连续生成不利球体
      }
    },

    // 失败率控制 - 确保大部分玩家在富二代阶段失败
    FAILURE_CONTROL: {
      TARGET_SUCCESS_RATE: 0.05, // 目标通关率5%
      RICH_STAGE_FAILURE_RATE: 0.85, // 富二代阶段失败率85%
      ADAPTIVE_DIFFICULTY: true, // 自适应难度调整

      // 动态难度调整
      DYNAMIC_ADJUSTMENT: {
        MONITOR_WINDOW: 10, // 监控最近10局游戏
        SUCCESS_THRESHOLD: 0.3, // 成功率超过30%时增加难度
        FAILURE_THRESHOLD: 0.95, // 失败率超过95%时降低难度
        ADJUSTMENT_STEP: 0.1 // 每次调整幅度
      }
    }
  }
}

// 导出默认配置
export default GameConfig 
