/**
 * 物理引擎管理器 - Matter.js版本
 * 统一管理Matter.js物理引擎实例，确保所有角色使用同一个引擎
 */

// 尝试导入Matter.js
let Matter
try {
  Matter = require('./matter.min.js')
} catch (error) {
  console.warn('无法通过require导入Matter.js，尝试使用全局变量')
}

class PhysicsManager {
  constructor() {
    this.engine = null
    this.world = null
    this.bodies = []
    this.ground = null
    this.walls = []
    this.isInitialized = false
    this.initPromise = null
  }

  /**
   * 初始化物理引擎
   */
  async init() {
    if (this.isInitialized) {
      return this.engine
    }

    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._initPhysicsEngine()
    return this.initPromise
  }

  /**
   * 内部初始化方法
   */
  async _initPhysicsEngine() {
    try {
      // console.log('物理引擎管理器：开始初始化Matter.js...')
      
      // 检查Matter是否可用
      if (typeof Matter === 'undefined') {
        throw new Error('Matter.js 未加载，请检查导入顺序')
      }
      
      console.log('Matter.js 可用，开始创建引擎...')
      
      // 创建引擎
      this.engine = Matter.Engine.create({
        enableSleeping: false,
        constraintIterations: 6,
        positionIterations: 12,
        velocityIterations: 8
      })
      
      this.world = this.engine.world
      
      // 设置重力 - 使用Matter.js默认重力
      this.world.gravity.x = 0
      this.world.gravity.y = 1
      
      // console.log('物理引擎创建成功，重力设置:', {
      //   x: this.world.gravity.x,
      //   y: this.world.gravity.y
      // })
      
      // 调试输出：确认物理引擎配置
      // console.log('物理引擎配置详情:')
      // console.log('- 引擎实例:', !!this.engine)
      // console.log('- 世界实例:', !!this.world)
      // console.log('- 重力:', this.world.gravity)
      // console.log('- 启用睡眠:', this.engine.enableSleeping)
      // console.log('- 约束迭代:', this.engine.constraintIterations)
      // console.log('- 位置迭代:', this.engine.positionIterations)
      // console.log('- 速度迭代:', this.engine.velocityIterations)
      
      this.isInitialized = true
      // console.log('物理引擎管理器：Matter.js 初始化成功')
      
      return this.engine
    } catch (error) {
      console.error('物理引擎管理器：初始化失败', error)
      this.isInitialized = false
      this.initPromise = null
      throw error
    }
  }

  /**
   * 获取物理引擎实例
   */
  getPhysicsEngine() {
    if (!this.isInitialized) {
      throw new Error('物理引擎未初始化，请先调用 init()')
    }
    return this.engine
  }

  /**
   * 获取世界实例
   */
  getWorld() {
    if (!this.isInitialized) {
      throw new Error('物理引擎未初始化，请先调用 init()')
    }
    return this.world
  }

  /**
   * 检查是否已初始化
   */
  isReady() {
    return this.isInitialized && this.engine !== null
  }

  /**
   * 创建圆形物理体
   */
  createCircleBody(x, y, radius, options = {}) {
    if (!this.isInitialized) {
      throw new Error('物理引擎未初始化')
    }

    const body = Matter.Bodies.circle(x, y, radius, {
      restitution: 0.3,
      friction: 0.3,
      density: 0.01,
      isStatic: false,
      sleepThreshold: Infinity,
      ...options
    })

    // 添加自定义属性
    body.circleRadius = radius
    body.label = options.label || 'circle'

    Matter.World.add(this.world, body)
    
    // 调试输出：确认球体创建

    
    return body
  }

  /**
   * 创建矩形物理体
   */
  createRectangleBody(x, y, width, height, options = {}) {
    if (!this.isInitialized) {
      throw new Error('物理引擎未初始化')
    }

    const body = Matter.Bodies.rectangle(x, y, width, height, {
      isStatic: true,
      ...options
    })

    Matter.World.add(this.world, body)
    return body
  }

  /**
   * 创建地面
   */
  createGround(x, y, width, height) {
    if (this.ground) {
      Matter.World.remove(this.world, this.ground)
    }

    this.ground = this.createRectangleBody(x, y, width, height, {
      isStatic: true,
      label: 'ground'
    })

    return this.ground
  }

  /**
   * 创建墙壁
   */
  createWalls(screenWidth, screenHeight, wallThickness = 50) {
    // 清除现有墙壁
    this.walls.forEach(wall => {
      Matter.World.remove(this.world, wall)
    })
    this.walls = []

    // 左墙
    const leftWall = this.createRectangleBody(
      -wallThickness / 2, 
      screenHeight / 2, 
      wallThickness, 
      screenHeight,
      { label: 'leftWall' }
    )
    this.walls.push(leftWall)

    // 右墙
    const rightWall = this.createRectangleBody(
      screenWidth + wallThickness / 2, 
      screenHeight / 2, 
      wallThickness, 
      screenHeight,
      { label: 'rightWall' }
    )
    this.walls.push(rightWall)

    // 底墙
    const bottomWall = this.createRectangleBody(
      screenWidth / 2, 
      screenHeight + wallThickness / 2, 
      screenWidth, 
      wallThickness,
      { label: 'bottomWall' }
    )
    this.walls.push(bottomWall)

    return this.walls
  }

  /**
   * 更新物理引擎
   */
  update(deltaTime = 16.67) {
    if (!this.isInitialized) {
      return
    }

    try {
      // 限制deltaTime在合理范围内
      const clampedDeltaTime = Math.min(Math.max(deltaTime, 8), 50)
      
      // 更新物理引擎
      Matter.Engine.update(this.engine, clampedDeltaTime)
      
    } catch (error) {
      console.error('物理引擎更新失败:', error)
    }
  }

  /**
   * 移除物理体
   */
  removeBody(body) {
    if (body && this.isInitialized) {
      Matter.World.remove(this.world, body)
    }
  }

  /**
   * 设置重力
   */
  setGravity(x, y) {
    if (this.isInitialized) {
      this.world.gravity.x = x
      this.world.gravity.y = y
    }
  }

  /**
   * 获取重力
   */
  getGravity() {
    if (this.isInitialized) {
      return {
        x: this.world.gravity.x,
        y: this.world.gravity.y
      }
    }
    return { x: 0, y: 0 }
  }

  /**
   * 重置物理引擎
   */
  reset() {
    if (this.engine) {
      // 清除所有物理体
      Matter.World.clear(this.world, false)
      this.bodies = []
      this.ground = null
      this.walls = []
    }
    
    this.engine = null
    this.world = null
    this.isInitialized = false
    this.initPromise = null

          // console.log('物理引擎已重置')
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    if (!this.engine) {
      return {
        isInitialized: false,
        engineType: 'none',
        bodyCount: 0
      }
    }

    return {
      isInitialized: this.isInitialized,
      engineType: 'matter.js',
      bodyCount: this.world.bodies.length,
      gravity: this.getGravity()
    }
  }

  /**
   * 获取所有物理体
   */
  getAllBodies() {
    if (!this.isInitialized) {
      return []
    }
    return this.world.bodies
  }

  /**
   * 检测碰撞
   */
  detectCollisions(body, callback) {
    if (!this.isInitialized) {
      return
    }

    const pairs = Matter.Query.collides(body, this.world.bodies)
    pairs.forEach(pair => {
      if (pair.bodyA !== body && pair.bodyB !== body) {
        callback(pair)
      }
    })
  }
}

// 创建全局单例
const physicsManager = new PhysicsManager()

// 导出单例
export { physicsManager }

// 导出兼容的初始化函数
export const initBox2D = () => physicsManager.init() 